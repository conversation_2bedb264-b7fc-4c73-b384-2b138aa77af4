using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace POSSystem.Converters
{
    public class CurrencyFormatConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value == null) return GetDefaultCurrencyFormat(0.00m);

            try
            {
                decimal amount = System.Convert.ToDecimal(value);
                var currencyFormat = Application.Current.Resources["CurrencyFormat"] as string;
                if (string.IsNullOrEmpty(currencyFormat))
                {
                    return GetDefaultCurrencyFormat(amount);
                }
                return string.Format(currencyFormat, amount);
            }
            catch (FormatException ex)
            {
                // ✅ CRITICAL FIX: Log FormatException details for debugging
                System.Diagnostics.Debug.WriteLine($"[CONVERTER] CurrencyFormatConverter FormatException: {ex.Message}, Value: {value}");
                return GetDefaultCurrencyFormat(0.00m);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[CONVERTER] CurrencyFormatConverter Error: {ex.Message}, Value: {value}");
                return GetDefaultCurrencyFormat(0.00m);
            }
        }

        private string GetDefaultCurrencyFormat(decimal amount)
        {
            // Try to get the currency symbol from resources, fallback to DA
            var currencySymbol = Application.Current.Resources["CurrencySymbol"] as string ?? "DA";
            return $"{amount:N2} {currencySymbol}";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is string stringValue)
            {
                stringValue = stringValue.Replace("د.ج", "").Replace("DA", "").Trim();
                if (decimal.TryParse(stringValue, out decimal result))
                {
                    return result;
                }
            }
            return 0.00m;
        }
    }
} 