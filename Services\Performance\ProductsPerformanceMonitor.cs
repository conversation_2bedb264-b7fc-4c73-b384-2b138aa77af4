using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace POSSystem.Services.Performance
{
    /// <summary>
    /// Dedicated performance monitoring service for ProductsViewModel operations
    /// Tracks performance metrics and provides detailed analysis
    /// </summary>
    public class ProductsPerformanceMonitor
    {
        private readonly ConcurrentDictionary<string, List<long>> _performanceMetrics;
        private readonly ConcurrentDictionary<string, Stopwatch> _activeOperations;
        private readonly string _logFilePath;
        private readonly object _logLock = new object();

        // Performance thresholds
        private const long EXCELLENT_THRESHOLD = 200;  // Under 200ms is excellent
        private const long GOOD_THRESHOLD = 500;       // Under 500ms is good (our target)
        private const long WARNING_THRESHOLD = 1000;   // Over 1000ms is concerning
        private const long CRITICAL_THRESHOLD = 3000;  // Over 3000ms is critical

        public ProductsPerformanceMonitor()
        {
            _performanceMetrics = new ConcurrentDictionary<string, List<long>>();
            _activeOperations = new ConcurrentDictionary<string, Stopwatch>();
            
            // Create performance log file
            var logDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
            Directory.CreateDirectory(logDir);
            _logFilePath = Path.Combine(logDir, $"products_performance_{DateTime.Now:yyyy-MM-dd}.log");
            
            LogMessage("ProductsPerformanceMonitor initialized");
        }

        /// <summary>
        /// Start monitoring an operation
        /// </summary>
        public string StartOperation(string operationName, string details = null)
        {
            var operationId = Guid.NewGuid().ToString("N")[..8];
            var fullOperationName = $"{operationName}_{operationId}";
            
            var stopwatch = Stopwatch.StartNew();
            _activeOperations.TryAdd(fullOperationName, stopwatch);
            
            var message = $"[START] {operationName} (ID: {operationId})";
            if (!string.IsNullOrEmpty(details))
                message += $" - {details}";
                
            LogMessage(message);
            Debug.WriteLine($"[PERF-MONITOR] {message}");
            
            return operationId;
        }

        /// <summary>
        /// Stop monitoring an operation and record the result
        /// </summary>
        public long StopOperation(string operationName, string operationId, string details = null)
        {
            var fullOperationName = $"{operationName}_{operationId}";
            
            if (!_activeOperations.TryRemove(fullOperationName, out var stopwatch))
            {
                LogMessage($"[ERROR] Operation not found: {fullOperationName}");
                return 0;
            }

            stopwatch.Stop();
            var elapsed = stopwatch.ElapsedMilliseconds;
            
            // Record the metric
            _performanceMetrics.AddOrUpdate(operationName, 
                new List<long> { elapsed }, 
                (key, list) => 
                {
                    list.Add(elapsed);
                    // Keep only last 100 measurements to prevent memory bloat
                    if (list.Count > 100)
                        list.RemoveRange(0, list.Count - 100);
                    return list;
                });

            // Determine performance level
            var performanceLevel = GetPerformanceLevel(elapsed);
            var emoji = GetPerformanceEmoji(performanceLevel);
            
            var message = $"[{performanceLevel}] {emoji} {operationName} (ID: {operationId}) completed in {elapsed}ms";
            if (!string.IsNullOrEmpty(details))
                message += $" - {details}";
                
            LogMessage(message);
            Debug.WriteLine($"[PERF-MONITOR] {message}");
            
            // Log statistics periodically
            if (_performanceMetrics[operationName].Count % 10 == 0)
            {
                LogOperationStatistics(operationName);
            }
            
            return elapsed;
        }

        /// <summary>
        /// Get performance statistics for an operation
        /// </summary>
        public PerformanceStatistics GetStatistics(string operationName)
        {
            if (!_performanceMetrics.TryGetValue(operationName, out var metrics) || !metrics.Any())
            {
                return new PerformanceStatistics
                {
                    OperationName = operationName,
                    Count = 0,
                    AverageMs = 0,
                    MinMs = 0,
                    MaxMs = 0,
                    PerformanceLevel = PerformanceLevel.Unknown
                };
            }

            var average = (long)metrics.Average();
            var min = metrics.Min();
            var max = metrics.Max();
            
            return new PerformanceStatistics
            {
                OperationName = operationName,
                Count = metrics.Count,
                AverageMs = average,
                MinMs = min,
                MaxMs = max,
                PerformanceLevel = GetPerformanceLevel(average),
                RecentTrend = GetRecentTrend(metrics)
            };
        }

        /// <summary>
        /// Get all performance statistics
        /// </summary>
        public List<PerformanceStatistics> GetAllStatistics()
        {
            return _performanceMetrics.Keys
                .Select(GetStatistics)
                .OrderByDescending(s => s.Count)
                .ToList();
        }

        /// <summary>
        /// Log comprehensive performance report
        /// </summary>
        public void LogPerformanceReport()
        {
            var report = GeneratePerformanceReport();
            LogMessage(report);
            Debug.WriteLine($"[PERF-MONITOR] Performance Report:\n{report}");
        }

        /// <summary>
        /// Clear all performance data
        /// </summary>
        public void ClearMetrics()
        {
            _performanceMetrics.Clear();
            LogMessage("Performance metrics cleared");
        }

        private void LogOperationStatistics(string operationName)
        {
            var stats = GetStatistics(operationName);
            var message = $"[STATS] {operationName}: Avg={stats.AverageMs}ms, Min={stats.MinMs}ms, Max={stats.MaxMs}ms, Count={stats.Count}, Trend={stats.RecentTrend}";
            LogMessage(message);
        }

        private string GeneratePerformanceReport()
        {
            var report = new List<string>
            {
                "=== PRODUCTS PERFORMANCE REPORT ===",
                $"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}",
                ""
            };

            var allStats = GetAllStatistics();
            
            if (!allStats.Any())
            {
                report.Add("No performance data available.");
                return string.Join(Environment.NewLine, report);
            }

            foreach (var stats in allStats)
            {
                var emoji = GetPerformanceEmoji(stats.PerformanceLevel);
                report.Add($"{emoji} {stats.OperationName}:");
                report.Add($"   Average: {stats.AverageMs}ms ({stats.PerformanceLevel})");
                report.Add($"   Range: {stats.MinMs}ms - {stats.MaxMs}ms");
                report.Add($"   Count: {stats.Count} operations");
                report.Add($"   Trend: {stats.RecentTrend}");
                report.Add("");
            }

            // Add recommendations
            report.Add("=== RECOMMENDATIONS ===");
            foreach (var stats in allStats.Where(s => s.PerformanceLevel == PerformanceLevel.Warning || s.PerformanceLevel == PerformanceLevel.Critical))
            {
                report.Add($"⚠️ {stats.OperationName}: Consider optimization (avg: {stats.AverageMs}ms)");
            }

            return string.Join(Environment.NewLine, report);
        }

        private PerformanceLevel GetPerformanceLevel(long milliseconds)
        {
            if (milliseconds <= EXCELLENT_THRESHOLD) return PerformanceLevel.Excellent;
            if (milliseconds <= GOOD_THRESHOLD) return PerformanceLevel.Good;
            if (milliseconds <= WARNING_THRESHOLD) return PerformanceLevel.Warning;
            if (milliseconds <= CRITICAL_THRESHOLD) return PerformanceLevel.Critical;
            return PerformanceLevel.Unacceptable;
        }

        private string GetPerformanceEmoji(PerformanceLevel level)
        {
            return level switch
            {
                PerformanceLevel.Excellent => "🚀",
                PerformanceLevel.Good => "✅",
                PerformanceLevel.Warning => "⚠️",
                PerformanceLevel.Critical => "🔥",
                PerformanceLevel.Unacceptable => "💥",
                _ => "❓"
            };
        }

        private TrendDirection GetRecentTrend(List<long> metrics)
        {
            if (metrics.Count < 5) return TrendDirection.Stable;
            
            var recent = metrics.TakeLast(5).ToList();
            var older = metrics.Count >= 10 ? metrics.TakeLast(10).Take(5).ToList() : recent;
            
            var recentAvg = recent.Average();
            var olderAvg = older.Average();
            
            var difference = (recentAvg - olderAvg) / olderAvg;
            
            if (difference > 0.1) return TrendDirection.Degrading;
            if (difference < -0.1) return TrendDirection.Improving;
            return TrendDirection.Stable;
        }

        private void LogMessage(string message)
        {
            try
            {
                lock (_logLock)
                {
                    var logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} {message}";
                    File.AppendAllText(_logFilePath, logEntry + Environment.NewLine);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[PERF-MONITOR] Failed to log message: {ex.Message}");
            }
        }
    }

    public class PerformanceStatistics
    {
        public string OperationName { get; set; }
        public int Count { get; set; }
        public long AverageMs { get; set; }
        public long MinMs { get; set; }
        public long MaxMs { get; set; }
        public PerformanceLevel PerformanceLevel { get; set; }
        public TrendDirection RecentTrend { get; set; }
    }

    public enum PerformanceLevel
    {
        Unknown,
        Excellent,    // < 200ms
        Good,         // < 500ms
        Warning,      // < 1000ms
        Critical,     // < 3000ms
        Unacceptable  // >= 3000ms
    }

    public enum TrendDirection
    {
        Improving,
        Stable,
        Degrading
    }
}
