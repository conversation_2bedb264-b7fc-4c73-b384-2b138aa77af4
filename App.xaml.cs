using System;
using System.Windows;
using System.Globalization;
using System.Threading;
using System.Configuration;
using System.Windows.Markup;
using POSSystem.Services;
using POSSystem.Services.Interfaces;
using POSSystem.Views;
using POSSystem.ViewModels;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using System.Linq;
using System.Windows.Controls;
using System.Windows.Media;
using System.ComponentModel;
using System.Windows.Data;
using System.Diagnostics;
using System.IO;
using System.Diagnostics.Tracing;
using MaterialDesignThemes.Wpf;
using Microsoft.Data.Sqlite;
using BCrypt.Net;
using POSSystem.Helpers;
using POSSystem.Utilities;
using POSSystem.Services.Dashboard;
using POSSystem.Services.Performance;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace POSSystem
{
    /// <summary>
    /// Interaction logic for App.xaml
    /// </summary>
    public partial class App : Application
    {
        // Store a reference to services that need proper cleanup
        private ILicenseService _licenseService;
        private static Mutex _mutex = null;
        private IServiceProvider _serviceProvider;
        private IHost _host;
        private static volatile bool _isShuttingDown = false;

        // Add public static service provider for legacy compatibility
        public static IServiceProvider ServiceProvider { get; private set; }

        // Legacy property for backward compatibility
        public static IDatabaseService DbService => ServiceProvider?.GetService<IDatabaseService>();

        public App()
        {
            // ✅ ENHANCED DEBUG FILE LOGGING: Set up comprehensive logging to capture all debug output
            SetupEnhancedDebugLogging();

            // Log application start
            Trace.WriteLine($"Application started at {DateTime.Now}");
            Trace.WriteLine($"Base Directory: {AppDomain.CurrentDomain.BaseDirectory}");

            // Log assembly location
            var assemblyLocation = System.Reflection.Assembly.GetExecutingAssembly().Location;
            Trace.WriteLine($"Assembly Location: {assemblyLocation}");

            // Apply SQL function fixes to correct typos like "INSTRO" vs "INSTR"
            try
            {
                // Apply the SQL function fixes
                SqlFunctionFix.ApplyFixes();

                Trace.WriteLine("SQL function fixes applied successfully");
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"Warning: SQL function fixes could not be applied: {ex.Message}");
            }

            // Register for application exit
            Exit += App_Exit;
        }

        protected override async void OnStartup(StartupEventArgs e)
        {
            // Add trace listener to write debug output to file
            var traceListener = new System.Diagnostics.TextWriterTraceListener("debug_output.log");
            Trace.Listeners.Add(traceListener);
            Trace.AutoFlush = true;

            // Test trace output
            Trace.WriteLine("=== APP.XAML.CS STARTUP DEBUG ===");
            Trace.WriteLine("Trace listener added successfully");

            // Check for existing instance
            _mutex = new Mutex(true, "POSSystemSingleInstanceMutex", out bool createdNew);

            // Check if this is a user switch request
            bool isUserSwitch = e.Args.Length > 0 && e.Args[0].ToLower() == "switch-user";

            if (!createdNew)
            {
                if (isUserSwitch)
                {
                    // Try to find and activate the existing window
                    ActivateExistingInstance();
                }
                else
                {
                    MessageBox.Show("An instance of the application is already running.", "POS System", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                _mutex = null;
                Shutdown();
                return;
            }

            base.OnStartup(e);

            // Log startup
            Trace.WriteLine("Application OnStartup called");



            // Apply performance optimizations
            Trace.WriteLine("Applying performance optimizations");
            try
            {
                // Apply global performance optimizations
                PerformanceOptimizer.OptimizeApplication();

                // ✅ CRITICAL PERFORMANCE FIX: Initialize UI Performance Optimizer to address low frame rates
                _ = UIPerformanceOptimizer.Instance;

                Trace.WriteLine("Performance optimizations applied successfully");

                // Apply Performance Lite UI overrides if enabled
                try
                {
                    var settings = _serviceProvider?.GetService<ISettingsService>() ?? new SettingsService();
                    var perfLite = settings.GetSetting("PerformanceLiteUI");
                    if (!string.IsNullOrEmpty(perfLite) && perfLite.Equals("true", StringComparison.OrdinalIgnoreCase))
                    {
                        Trace.WriteLine("Performance Lite UI enabled - merging low-cost resource overrides");
                        Resources.MergedDictionaries.Add(new ResourceDictionary
                        {
                            Source = new Uri("Resources/PerformanceLite.xaml", UriKind.Relative)
                        });
                    }

                    // Apply multi-level Performance Profile if set
                    var profile = settings.GetSetting("PerformanceProfile") ?? "Standard";
                    switch (profile)
                    {
                        case "Lite":
                            Trace.WriteLine("Performance Profile: Lite - merging lite overrides");
                            Resources.MergedDictionaries.Add(new ResourceDictionary { Source = new Uri("Resources/PerformanceProfile.Lite.xaml", UriKind.Relative) });
                            break;
                        case "UltraLite":
                            Trace.WriteLine("Performance Profile: UltraLite - merging ultra-lite overrides");
                            Resources.MergedDictionaries.Add(new ResourceDictionary { Source = new Uri("Resources/PerformanceProfile.UltraLite.xaml", UriKind.Relative) });
                            break;
                        default:
                            Trace.WriteLine("Performance Profile: Standard");
                            Resources.MergedDictionaries.Add(new ResourceDictionary { Source = new Uri("Resources/PerformanceProfile.Standard.xaml", UriKind.Relative) });
                            break;
                    }
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"Error applying Performance Lite UI overrides: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"Error applying performance optimizations: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Trace.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }

            // Check for command line parameters
            if (e.Args.Length > 0)
            {
                if (e.Args[0].ToLower() == "clean-security")
                {
                    try
                    {
                        MessageBox.Show("Cleaning security files...", "Security Cleanup", MessageBoxButton.OK, MessageBoxImage.Information);
                        // Initialize and clean security files
                        var licenseService = new LicenseService();
                        licenseService.CleanSecurityFiles();
                        MessageBox.Show("Security files cleaned successfully. Please restart the application.", "Security Cleanup Complete", MessageBoxButton.OK, MessageBoxImage.Information);
                        Shutdown();
                        return;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error cleaning security files: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        Shutdown();
                        return;
                    }
                }
            }

            // Set up global exception handling
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;

            try
            {
                // ✅ CRITICAL STARTUP OPTIMIZATION: Initialize with optimized startup orchestrator
                await InitializeWithOptimizedStartupAsync();

                // Get services from DI container
                var settingsService = _serviceProvider.GetRequiredService<ISettingsService>();
                var themeService = _serviceProvider.GetRequiredService<IThemeService>();

                // Initialize license service and store the reference for cleanup
                _licenseService = _serviceProvider.GetRequiredService<ILicenseService>();

                // Check for development mode to bypass license activation popup
                Trace.WriteLine($"=== LICENSE VALIDATION DEBUG ===");
                Trace.WriteLine($"DevModeDisabled config setting: {ConfigurationManager.AppSettings["DevModeDisabled"]}");

                // Check DevModeDisabled setting first (highest priority)
                string devModeDisabledSetting = ConfigurationManager.AppSettings["DevModeDisabled"];
                bool devModeDisabled = !string.IsNullOrEmpty(devModeDisabledSetting) &&
                                     (devModeDisabledSetting.Equals("true", StringComparison.OrdinalIgnoreCase) ||
                                      devModeDisabledSetting.Equals("1", StringComparison.OrdinalIgnoreCase));

                if (devModeDisabled)
                {
                    Trace.WriteLine("DevModeDisabled is set to true - bypassing license validation entirely");
                }
                else
                {
                    // DevModeDisabled is false, so check if we should run license validation
                    bool devModeEnabled = IsDevModeEnabled();
                    Trace.WriteLine($"Development mode enabled (from IsDevModeEnabled): {devModeEnabled}");

                    // If DevModeDisabled is explicitly set to false, force license validation regardless of DEBUG build
                    bool shouldRunLicenseValidation = !string.IsNullOrEmpty(devModeDisabledSetting) &&
                                                    devModeDisabledSetting.Equals("false", StringComparison.OrdinalIgnoreCase);

                    if (shouldRunLicenseValidation || !devModeEnabled)
                    {
                        Trace.WriteLine("Running license validation (DevModeDisabled=false or not in dev mode)");
                        bool licenseValid = _licenseService.ValidateLicense();
                        Trace.WriteLine($"License validation result: {licenseValid}");

                        if (!licenseValid)
                        {
                            Trace.WriteLine("License invalid - showing activation popup");
                            var activationWindow = new LicenseActivationView
                            {
                                DataContext = _serviceProvider.GetRequiredService<LicenseActivationViewModel>()
                            };

                            if (activationWindow.ShowDialog() != true)
                            {
                                Trace.WriteLine("Activation popup cancelled - shutting down application");
                                Shutdown();
                                return;
                            }
                            else
                            {
                                Trace.WriteLine("Activation popup completed successfully");
                            }
                        }
                        else
                        {
                            Trace.WriteLine("License is valid - no activation popup needed");
                        }
                    }
                    else
                    {
                        Trace.WriteLine("Development mode is ENABLED and DevModeDisabled is not explicitly false - bypassing license validation");
                    }
                }

                // ✅ PERFORMANCE FIX: Initialize database asynchronously to improve startup time
                var dbService = _serviceProvider.GetRequiredService<IDatabaseService>();
                try
                {
                    // Test database connection by retrieving a simple value asynchronously
                    await Task.Run(async () =>
                    {
                        var users = dbService.GetAllUsers();
                        Trace.WriteLine($"Database initialized successfully. Found {users.Count} users.");

                        // Migrate passwords if needed
                        dbService.MigratePasswords();
                    });
                }
                catch (Exception dbEx)
                {
                    Trace.WriteLine($"Database initialization error: {dbEx.Message}");
                    MessageBox.Show($"Database error: {dbEx.Message}. The application will now exit.",
                        "Database Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    Shutdown();
                    return;
                }

                // Initialize language settings FIRST
                InitializeLanguage();

                // THEN Load and apply theme settings
                var isDarkTheme = settingsService.GetSetting("IsDarkTheme")?.ToLower() == "true";
                var savedColorStr = settingsService.GetSetting("ThemeColor") ?? "#2196F3"; // Default blue
                var themeColor = (Color)ColorConverter.ConvertFromString(savedColorStr);

                // Apply the theme with saved settings without using MaterialDesignTheme.Current
                themeService.ApplyCustomTheme(isDarkTheme, themeColor);

                // Re-apply language settings after theme changes to ensure they're not overridden
                string language = ConfigurationManager.AppSettings["Language"] ?? "en";
                if (!string.IsNullOrEmpty(language))
                {
                    ApplyLanguage(language);
                }

                // Check for auto-login configuration
                Trace.WriteLine("=== AUTO-LOGIN CONFIGURATION CHECK ===");
                string autoLoginSetting = ConfigurationManager.AppSettings["AutoLoginEnabled"];
                Trace.WriteLine($"AutoLoginEnabled config setting: '{autoLoginSetting}'");

                bool autoLoginEnabled = !string.IsNullOrEmpty(autoLoginSetting) &&
                                       (autoLoginSetting.Equals("true", StringComparison.OrdinalIgnoreCase) ||
                                        autoLoginSetting.Equals("1", StringComparison.OrdinalIgnoreCase));

                if (autoLoginEnabled)
                {
                    Trace.WriteLine("Auto-login is ENABLED - bypassing login screen");

                    // Ensure admin user exists
                    EnsureAdminUserExists(dbService);

                    // Create authentication service and manually perform login
                    var authService = _serviceProvider.GetRequiredService<IAuthenticationService>();
                    Trace.WriteLine("Attempting auto-login with admin credentials (username: admin, password: admin123)");
                    bool loginSuccess = authService.Login("admin", "admin123");
                    Trace.WriteLine($"Auto-login result: {loginSuccess}");

                    if (loginSuccess)
                    {
                        Trace.WriteLine("Auto-login successful - opening main window directly");
                        // Go directly to main window
                        var mainWindow = new MainWindow(authService);

                        // Apply performance optimizations to main window
                        PerformanceOptimizer.OptimizeElement(mainWindow);

                        mainWindow.Show();
                    }
                    else
                    {
                        // If auto-login fails, fall back to normal login
                        Trace.WriteLine("Auto-login failed - falling back to normal login screen");
                        ShowLoginWindow();
                    }
                }
                else
                {
                    Trace.WriteLine("Auto-login is DISABLED - showing normal login screen");
                    // Normal login flow
                    ShowLoginWindow();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error starting application: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        /// <summary>
        /// Initializes the dependency injection container
        /// </summary>
        private void InitializeDependencyInjection()
        {
            try
            {
                // Create host builder with services
                _host = ServiceConfiguration.CreateHostBuilder().Build();
                _serviceProvider = _host.Services;

                // Set static reference for legacy compatibility
                ServiceProvider = _serviceProvider;

                // Validate service registration in debug mode
                #if DEBUG
                if (!DIValidationTest.ValidateServiceRegistration())
                {
                    throw new InvalidOperationException("Service registration validation failed");
                }

                // Also validate ViewModels
                if (!DIValidationTest.ValidateViewModelRegistration())
                {
                    throw new InvalidOperationException("ViewModel registration validation failed");
                }

                // ✅ PERFORMANCE MONITORING: Enable UI performance monitoring in debug mode
                POSSystem.Helpers.UIPerformanceMonitor.IsEnabled = true;
                POSSystem.Helpers.UIPerformanceMonitor.StartUIResponsivenessMonitoring();
                Trace.WriteLine("UI Performance monitoring enabled");
                #endif

                // PERFORMANCE: Remove legacy singleton dashboard preload to avoid duplicate background loaders
                // DashboardPreloadManager.Initialize();
                Trace.WriteLine("Dashboard preload service managed by hosted service (BackgroundServices.DashboardPreloadService)");

                // ✅ MEMORY CRITICAL: Initialize advanced memory manager
                var memoryManager = _serviceProvider.GetService<POSSystem.Services.Memory.AdvancedMemoryManager>();
                if (memoryManager != null)
                {
                    Trace.WriteLine("✅ Advanced Memory Manager initialized successfully");
                }
                        // Expose memory manager via resources for UI components that cannot DI
                        if (Application.Current != null)
                        {
                            Application.Current.Resources["AdvancedMemoryManager"] = memoryManager;
                        }


                // ✅ DATABASE CRITICAL: Initialize database optimization services
                var indexOptimizer = _serviceProvider.GetService<POSSystem.Services.QueryOptimization.DatabaseIndexOptimizer>();
                if (indexOptimizer != null)
                {
                    Trace.WriteLine("✅ Database Index Optimizer initialized successfully");

                    // Apply essential indexes in background to prevent startup delay
                    _ = Task.Run(async () =>
                    {
                        try
                        {
                            await indexOptimizer.ApplyEssentialIndexesAsync();
                            Trace.WriteLine("✅ Essential database indexes applied successfully");
                        }
                        catch (Exception ex)
                        {
                            Trace.WriteLine($"❌ Error applying database indexes: {ex.Message}");
                        }
                    });
                }

                // ✅ UI CRITICAL: Initialize UI rendering optimization services
                var uiRenderingMonitor = _serviceProvider.GetService<POSSystem.Services.UI.UIRenderingPerformanceMonitor>();
                if (uiRenderingMonitor != null)
                {
                    POSSystem.Helpers.AdvancedUIOptimizer.Initialize(uiRenderingMonitor);
                    Trace.WriteLine("✅ UI Rendering Performance Monitor initialized successfully");
                }

                // ✅ PERFORMANCE VALIDATION: Run quick performance validation
                _ = Task.Run(async () =>
                {
                    try
                    {
                        // Quick validation of services
                        var dbService = _serviceProvider.GetService<IDatabaseService>();
                        var asyncDbService = _serviceProvider.GetService<AsyncDatabaseService>();
                        var isValid = dbService != null && asyncDbService != null;
                        Trace.WriteLine($"Performance validation result: {(isValid ? "✅ PASSED" : "❌ FAILED")}");
                        Trace.WriteLine($"DatabaseService: {(dbService != null ? "✅" : "❌")}");
                        Trace.WriteLine($"AsyncDatabaseService: {(asyncDbService != null ? "✅" : "❌")}");

                        // Memory manager validation
                        if (memoryManager != null)
                        {
                            var stats = memoryManager.GetMemoryStats();
                            Trace.WriteLine($"Memory Manager: ✅ Current: {stats.CurrentMB}MB");
                        }
                    }
                    catch (Exception ex)
                    {
                        Trace.WriteLine($"Performance validation error: {ex.Message}");
                    }
                });

                Trace.WriteLine("Dependency injection container initialized successfully");
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"Error initializing dependency injection: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// ✅ CRITICAL STARTUP OPTIMIZATION: Initialize with optimized startup orchestrator
        /// </summary>
        private async Task InitializeWithOptimizedStartupAsync()
        {
            try
            {
                Trace.WriteLine("🚀 Initializing with optimized startup orchestrator");

                // Create host builder with services (including startup optimization services)
                _host = ServiceConfiguration.CreateHostBuilder().Build();
                _serviceProvider = _host.Services;

                // Set static reference for legacy compatibility
                ServiceProvider = _serviceProvider;

                // Validate service registration in debug mode
                #if DEBUG
                if (!DIValidationTest.ValidateServiceRegistration())
                {
                    throw new InvalidOperationException("Service registration validation failed");
                }

                // Also validate ViewModels
                if (!DIValidationTest.ValidateViewModelRegistration())
                {
                    throw new InvalidOperationException("ViewModel registration validation failed");
                }

                // ✅ PERFORMANCE MONITORING: Enable UI performance monitoring in debug mode
                POSSystem.Helpers.UIPerformanceMonitor.IsEnabled = true;
                POSSystem.Helpers.UIPerformanceMonitor.StartUIResponsivenessMonitoring();
                Trace.WriteLine("UI Performance monitoring enabled");
                #endif

                // ✅ CRITICAL STARTUP OPTIMIZATION: Use optimized startup orchestrator
                try
                {
                    Trace.WriteLine("Attempting to get OptimizedStartupOrchestrator from DI container...");

                    // ✅ SIMPLIFIED: Skip orchestrator testing since we're using basic startup

                    // ✅ STARTUP OPTIMIZATION: Disable optimized startup to avoid service disposal issues
                    var useOptimizedStartup = false; // Disabled - use basic startup to avoid ObjectDisposedException

                    if (useOptimizedStartup)
                    {
                        var startupOrchestrator = _serviceProvider.GetService<POSSystem.Services.Startup.OptimizedStartupOrchestrator>();

                        if (startupOrchestrator != null)
                        {
                            Trace.WriteLine("OptimizedStartupOrchestrator found, executing optimized startup...");

                            // Execute optimized startup with splash screen and parallel initialization
                            await startupOrchestrator.ExecuteOptimizedStartupAsync();

                            // Generate and log startup report
                            var startupReport = startupOrchestrator.GetStartupReport();
                            Trace.WriteLine(startupReport);

                            Trace.WriteLine("🎉 Optimized startup orchestrator completed successfully");
                        }
                        else
                        {
                            Trace.WriteLine("⚠️ OptimizedStartupOrchestrator not found in DI container, using fallback...");
                            await ExecuteBasicStartupInitializationAsync();
                        }
                    }
                    else
                    {
                        Trace.WriteLine("Using basic startup initialization (optimized startup disabled for debugging)...");
                        await ExecuteBasicStartupInitializationAsync();
                    }
                }
                catch (Exception orchestratorEx)
                {
                    Trace.WriteLine($"❌ Error with startup process: {orchestratorEx.Message}");
                    Trace.WriteLine($"Stack trace: {orchestratorEx.StackTrace}");
                    Trace.WriteLine("Falling back to basic startup initialization...");

                    // Fallback to basic initialization if orchestrator fails
                    await ExecuteBasicStartupInitializationAsync();
                }

                Trace.WriteLine("✅ Dependency injection and startup optimization completed successfully");
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"❌ Error during optimized startup initialization: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// ✅ FALLBACK: Basic startup initialization if orchestrator is not available
        /// </summary>
        private async Task ExecuteBasicStartupInitializationAsync()
        {
            try
            {
                Trace.WriteLine("⚠️ Using fallback basic startup initialization");

                // PERFORMANCE: Legacy singleton dashboard preload disabled; using hosted background service instead
                try
                {
                    // DashboardPreloadManager.Initialize();
                    Trace.WriteLine("✅ Dashboard preload service will be started by hosted service registration");
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"❌ Error initializing dashboard preload service (legacy disabled): {ex.Message}");
                }

                // ✅ MEMORY CRITICAL: Initialize advanced memory manager
                try
                {
                    var memoryManager = _serviceProvider.GetService<POSSystem.Services.Memory.AdvancedMemoryManager>();
                    if (memoryManager != null)
                    {
                        Trace.WriteLine("✅ Advanced Memory Manager initialized successfully");
                    }
                    else
                    {
                        Trace.WriteLine("⚠️ Advanced Memory Manager not available");
                    }
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"❌ Error initializing memory manager: {ex.Message}");
                }

                // ✅ REAL-TIME: Initialize sales event cache invalidation service (wires global sale events)
                try
                {
                    var cacheInvalidationService = _serviceProvider.GetService<POSSystem.Services.RealTime.SalesEventCacheInvalidationService>();
                    if (cacheInvalidationService != null)
                    {
                        Trace.WriteLine("✅ Sales Event Cache Invalidation Service initialized");
                    }
                    else
                    {
                        Trace.WriteLine("⚠️ Sales Event Cache Invalidation Service not available");
                    }
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"❌ Error initializing Sales Event Cache Invalidation Service: {ex.Message}");
                }





                // ✅ DATABASE CRITICAL: Initialize database optimization services
                try
                {
                    var indexOptimizer = _serviceProvider.GetService<POSSystem.Services.QueryOptimization.DatabaseIndexOptimizer>();
                    if (indexOptimizer != null)
                    {
                        Trace.WriteLine("✅ Database Index Optimizer initialized successfully");

                        // Apply essential indexes in background to prevent startup delay
                        _ = Task.Run(async () =>
                        {
                            try
                            {
                                await indexOptimizer.ApplyEssentialIndexesAsync();
                                Trace.WriteLine("✅ Essential database indexes applied successfully");
                            }
                            catch (Exception ex)
                            {
                                Trace.WriteLine($"❌ Error applying database indexes: {ex.Message}");
                            }
                        });
                    }
                    else
                    {
                        Trace.WriteLine("⚠️ Database Index Optimizer not available");
                    }
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"❌ Error initializing database optimizer: {ex.Message}");
                }

                // ✅ UI CRITICAL: Initialize UI rendering optimization services
                try
                {
                    var uiRenderingMonitor = _serviceProvider.GetService<POSSystem.Services.UI.UIRenderingPerformanceMonitor>();
                    if (uiRenderingMonitor != null)
                    {
                        POSSystem.Helpers.AdvancedUIOptimizer.Initialize(uiRenderingMonitor);
                        Trace.WriteLine("✅ UI Rendering Performance Monitor initialized successfully");
                    }
                    else
                    {
                        Trace.WriteLine("⚠️ UI Rendering Performance Monitor not available");
                    }
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"❌ Error initializing UI rendering monitor: {ex.Message}");
                }

                Trace.WriteLine("✅ Basic startup initialization completed successfully");
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"❌ Error in basic startup initialization: {ex.Message}");
                Trace.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// ✅ DIAGNOSTIC: Test startup orchestrator creation to identify issues
        /// </summary>
        private async Task TestStartupOrchestratorCreationAsync()
        {
            try
            {
                Trace.WriteLine("🔍 Testing startup orchestrator creation...");

                // Test individual service creation
                var performanceMonitor = _serviceProvider.GetService<POSSystem.Services.Startup.StartupPerformanceMonitor>();
                Trace.WriteLine($"StartupPerformanceMonitor: {(performanceMonitor != null ? "✅ Available" : "❌ Not available")}");

                var serviceInitializer = _serviceProvider.GetService<POSSystem.Services.Startup.ParallelServiceInitializer>();
                Trace.WriteLine($"ParallelServiceInitializer: {(serviceInitializer != null ? "✅ Available" : "❌ Not available")}");

                var lazyLoadingManager = _serviceProvider.GetService<POSSystem.Services.Startup.LazyLoadingManager>();
                Trace.WriteLine($"LazyLoadingManager: {(lazyLoadingManager != null ? "✅ Available" : "❌ Not available")}");

                var progressiveUILoader = _serviceProvider.GetService<POSSystem.Services.Startup.ProgressiveUILoader>();
                Trace.WriteLine($"ProgressiveUILoader: {(progressiveUILoader != null ? "✅ Available" : "❌ Not available")}");

                // Test orchestrator creation
                var orchestrator = _serviceProvider.GetService<POSSystem.Services.Startup.OptimizedStartupOrchestrator>();
                Trace.WriteLine($"OptimizedStartupOrchestrator: {(orchestrator != null ? "✅ Available" : "❌ Not available")}");

                if (orchestrator != null)
                {
                    Trace.WriteLine("✅ All startup services are available and orchestrator can be created");
                }
                else
                {
                    Trace.WriteLine("❌ OptimizedStartupOrchestrator could not be created");
                }
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"❌ Error testing startup orchestrator creation: {ex.Message}");
                Trace.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        // Handle application exit to properly clean up resources
        private void App_Exit(object sender, ExitEventArgs e)
        {
            try
            {
                // Set shutdown flag to prevent new operations
                _isShuttingDown = true;

                Trace.WriteLine("🔄 [APP-EXIT] Starting application cleanup...");

                // PERFORMANCE: Legacy singleton dashboard preload disabled; hosted service will be disposed with host
                try
                {
                    // DashboardPreloadManager.Shutdown();
                    Trace.WriteLine("✅ [APP-EXIT] Dashboard preload hosted service will be disposed with DI host");
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"⚠️ [APP-EXIT] Error shutting down dashboard preload (legacy disabled): {ex.Message}");
                }

                // ✅ PERFORMANCE MONITORING: Log performance statistics on exit
                #if DEBUG
                try
                {
                    POSSystem.Helpers.UIPerformanceMonitor.LogAllStats();
                    Trace.WriteLine("✅ [APP-EXIT] UI performance stats logged");
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"⚠️ [APP-EXIT] Error logging UI performance stats: {ex.Message}");
                }
                #endif

                // ✅ SIMPLIFIED: Skip startup orchestrator disposal since we're using basic startup
                Trace.WriteLine("✅ [APP-EXIT] Using basic startup - no orchestrator to dispose");

                // Release the mutex if it exists
                if (_mutex != null)
                {
                    try
                    {
                        _mutex.ReleaseMutex();
                        Trace.WriteLine("✅ [APP-EXIT] Mutex released");
                    }
                    catch (ObjectDisposedException)
                    {
                        // Mutex was already disposed, ignore this exception
                        Trace.WriteLine("⚠️ [APP-EXIT] Mutex was already disposed when trying to release it");
                    }
                    catch (Exception ex)
                    {
                        // Log any other mutex-related exceptions
                        Trace.WriteLine($"⚠️ [APP-EXIT] Error releasing mutex: {ex.Message}");
                    }

                    try
                    {
                        _mutex.Dispose();
                        Trace.WriteLine("✅ [APP-EXIT] Mutex disposed");
                    }
                    catch (Exception ex)
                    {
                        // Log any exceptions during disposal
                        Trace.WriteLine($"⚠️ [APP-EXIT] Error disposing mutex: {ex.Message}");
                    }

                    _mutex = null;
                }

                // Clean up license service
                try
                {
                    _licenseService?.Shutdown();
                    Trace.WriteLine("✅ [APP-EXIT] License service shutdown");
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"⚠️ [APP-EXIT] Error shutting down license service: {ex.Message}");
                }

                // Dispose of the DI container and host LAST
                try
                {
                    if (_serviceProvider is IDisposable disposableProvider)
                    {
                        disposableProvider.Dispose();
                        Trace.WriteLine("✅ [APP-EXIT] Service provider disposed");
                    }

                    _host?.Dispose();
                    Trace.WriteLine("✅ [APP-EXIT] Host disposed");
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"⚠️ [APP-EXIT] Error disposing DI container: {ex.Message}");
                }

                Trace.WriteLine("✅ [APP-EXIT] Application cleanup completed successfully");
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"❌ [APP-EXIT] Critical error during application cleanup: {ex.Message}");
                Trace.WriteLine($"Stack trace: {ex.StackTrace}");
            }

            // Close any open trace listeners
            Trace.Flush();
            foreach (TraceListener listener in Trace.Listeners)
            {
                listener.Flush();
                listener.Close();
            }

            // Log application exit
            Trace.WriteLine($"Application exited at {DateTime.Now}");
        }

        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            // Log unhandled exceptions
            Trace.WriteLine($"Unhandled Exception: {e.Exception.Message}");
            Trace.WriteLine($"Stack Trace: {e.Exception.StackTrace}");
            if (e.Exception.InnerException != null)
            {
                Trace.WriteLine($"Inner Exception: {e.Exception.InnerException.Message}");
                Trace.WriteLine($"Inner Stack Trace: {e.Exception.InnerException.StackTrace}");
            }
        }

        private void InitializeLanguage()
        {
            try
            {
                Trace.WriteLine("\nInitializing Language...");

                // Always start with English resources first
                var englishDict = new ResourceDictionary();
                var assembly = System.Reflection.Assembly.GetExecutingAssembly();
                englishDict.Source = new Uri($"/{assembly.GetName().Name};component/Resources/Strings.en.xaml", UriKind.Relative);

                Trace.WriteLine("Removing existing language dictionaries...");
                // Remove any existing language dictionaries
                var toRemove = Current.Resources.MergedDictionaries
                    .Where(rd => rd?.Source != null && rd.Source.OriginalString.Contains("/Strings."))
                    .ToList();

                foreach (var rd in toRemove)
                {
                    Trace.WriteLine($"Removing dictionary: {rd.Source}");
                    Current.Resources.MergedDictionaries.Remove(rd);
                }

                Trace.WriteLine("Adding English dictionary as fallback...");
                // Add English dictionary
                Current.Resources.MergedDictionaries.Add(englishDict);

                // Get configured language
                string language = ConfigurationManager.AppSettings["Language"];
                Trace.WriteLine($"Configured language from settings: {language}");

                if (string.IsNullOrEmpty(language))
                {
                    // If no language is configured, default to English and save it
                    language = "en";
                    Trace.WriteLine("No language configured, defaulting to English");
                    var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);
                    if (config.AppSettings.Settings["Language"] == null)
                    {
                        config.AppSettings.Settings.Add("Language", language);
                    }
                    else
                    {
                        config.AppSettings.Settings["Language"].Value = language;
                    }
                    config.Save(ConfigurationSaveMode.Modified);
                    ConfigurationManager.RefreshSection("appSettings");
                }

                // If language is not English, load that language's resources
                if (language != "en")
                {
                    Trace.WriteLine($"Loading non-English language: {language}");
                    ApplyLanguage(language);
                }
                else
                {
                    Trace.WriteLine("Setting up English culture and LTR flow direction");
                    // Set culture for English
                    var culture = new CultureInfo(language);
                    Thread.CurrentThread.CurrentCulture = culture;
                    Thread.CurrentThread.CurrentUICulture = culture;

                    // Don't use OverrideMetadata here - we'll set flow direction when windows are created
                    // The default FlowDirection in WPF is already LeftToRight
                }

                // Verify resource loading
                Trace.WriteLine("\nVerifying resource loading:");
                foreach (var dict in Current.Resources.MergedDictionaries)
                {
                    Trace.WriteLine($"Loaded dictionary: {dict.Source}");
                    if (dict.Source?.OriginalString.Contains("/Strings.") == true)
                    {
                        // Check for some key discount-related resources
                        var keys = new[] { "ApplyDiscount", "DiscountType", "DiscountValue", "DiscountReason" };
                        foreach (var key in keys)
                        {
                            var resource = dict[key];
                            Trace.WriteLine($"Resource '{key}': {(resource != null ? "Found" : "Not found")}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"Error in InitializeLanguage: {ex.Message}");
                Trace.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error initializing language: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public static void ApplyLanguage(string language)
        {
            try
            {
                if (string.IsNullOrEmpty(language))
                {
                    language = "en";
                }

                Trace.WriteLine($"Attempting to apply language: {language}");

                // Set the culture for the current thread
                var culture = new CultureInfo(language);

                // For Arabic, enforce Hindu-Arabic numerals but don't modify the culture
                if (language == "ar")
                {
                    culture = (CultureInfo)culture.Clone();
                    culture.NumberFormat.DigitSubstitution = DigitShapes.None;
                    culture.NumberFormat.NativeDigits = new string[] { "0", "1", "2", "3", "4", "5", "6", "7", "8", "9" };
                }

                Thread.CurrentThread.CurrentCulture = culture;
                Thread.CurrentThread.CurrentUICulture = culture;
                CultureInfo.DefaultThreadCurrentCulture = culture;
                CultureInfo.DefaultThreadCurrentUICulture = culture;

                Trace.WriteLine("Removing existing language dictionaries...");
                // First, remove all existing language dictionaries
                var toRemove = Current.Resources.MergedDictionaries
                    .Where(rd => rd?.Source != null &&
                           (rd.Source.OriginalString.Contains("/Strings.") ||
                            rd.Source.OriginalString.Contains("\\Strings.")))
                    .ToList();

                foreach (var rd in toRemove)
                {
                    Current.Resources.MergedDictionaries.Remove(rd);
                }

                var assembly = System.Reflection.Assembly.GetExecutingAssembly();

                // First load the common strings that aren't language-specific
                try
                {
                    var commonStringsDict = new ResourceDictionary
                    {
                        Source = new Uri($"pack://application:,,,/{assembly.GetName().Name};component/Resources/Strings.xaml", UriKind.Absolute)
                    };
                    Current.Resources.MergedDictionaries.Add(commonStringsDict);
                }
                catch (Exception ex)
                {
                    Trace.WriteLine($"Error loading common strings: {ex.Message}");
                }

                // Load English dictionary as fallback
                var englishDict = new ResourceDictionary
                {
                    Source = new Uri($"pack://application:,,,/{assembly.GetName().Name};component/Resources/Strings.en.xaml", UriKind.Absolute)
                };
                Current.Resources.MergedDictionaries.Add(englishDict);

                // If not English, load the target language dictionary last so it takes precedence
                if (language != "en")
                {
                    try
                    {
                        var targetDict = new ResourceDictionary
                        {
                            Source = new Uri($"pack://application:,,,/{assembly.GetName().Name};component/Resources/Strings.{language}.xaml", UriKind.Absolute)
                        };
                        Current.Resources.MergedDictionaries.Add(targetDict);
                    }
                    catch (Exception ex)
                    {
                        Trace.WriteLine($"Error loading language dictionary: {ex.Message}");
                    }
                }

                // Set application-wide flow direction based on language
                var newFlowDirection = language == "ar" ? FlowDirection.RightToLeft : FlowDirection.LeftToRight;
                Trace.WriteLine($"Setting application flow direction to: {newFlowDirection}");

                // Update flow direction for all windows
                foreach (Window window in Current.Windows)
                {
                    if (window == null) continue;

                    try
                    {
                        window.FlowDirection = newFlowDirection;

                        // For main application window, also set on main content
                        if (window is MainWindow mainWindow)
                        {
                            if (mainWindow.Content is FrameworkElement mainContent)
                            {
                                mainContent.FlowDirection = newFlowDirection;
                            }

                            // For main window find any grids or panels and set their direction too
                            var panels = LogicalTreeHelper.GetChildren(mainWindow)
                                .OfType<Panel>()
                                .ToList();

                            foreach (var panel in panels)
                            {
                                panel.FlowDirection = newFlowDirection;
                            }
                        }

                        // Force layout update
                        window.UpdateLayout();
                    }
                    catch (Exception ex)
                    {
                        Trace.WriteLine($"Error setting flow direction: {ex.Message}");
                    }
                }

                Trace.WriteLine("Language applied successfully!");
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"Error in ApplyLanguage: {ex.Message}");
                Trace.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error applying language: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // New method to check if development mode is enabled
        private bool IsDevModeEnabled()
        {
            try
            {
                Trace.WriteLine("=== DEVELOPMENT MODE DETECTION DEBUG ===");
                Trace.WriteLine("IsDevModeEnabled() method called");

                // STEP 1: Check for force-disable mechanisms first

                // Check 1: Check for dev_mode_disabled file (highest priority)
                string devModeDisabledPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "dev_mode_disabled");
                Trace.WriteLine($"Checking for dev_mode_disabled file at: {devModeDisabledPath}");
                if (File.Exists(devModeDisabledPath))
                {
                    Trace.WriteLine("Development mode disabled: dev_mode_disabled file exists");
                    return false;
                }
                else
                {
                    Trace.WriteLine("dev_mode_disabled file does not exist");
                }

                // Check 2: Check for registry override
                try
                {
                    Trace.WriteLine("Checking registry for DevModeDisabled settings...");
                    // Try HKCU first (user-specific settings)
                    using (var key = Microsoft.Win32.Registry.CurrentUser.OpenSubKey("Software\\POSSystem"))
                    {
                        if (key != null)
                        {
                            var value = key.GetValue("DevModeDisabled") as string;
                            Trace.WriteLine($"HKCU registry DevModeDisabled value: {value}");
                            if (!string.IsNullOrEmpty(value) && value.ToLower() == "true")
                            {
                                Trace.WriteLine("Development mode disabled: HKCU registry key exists");
                                return false;
                            }
                        }
                        else
                        {
                            Trace.WriteLine("HKCU registry key does not exist");
                        }
                    }

                    // Then try HKLM (machine-wide settings)
                    using (var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey("Software\\POSSystem"))
                    {
                        if (key != null)
                        {
                            var value = key.GetValue("DevModeDisabled") as string;
                            Trace.WriteLine($"HKLM registry DevModeDisabled value: {value}");
                            if (!string.IsNullOrEmpty(value) && value.ToLower() == "true")
                            {
                                Trace.WriteLine("Development mode disabled: HKLM registry key exists");
                                return false;
                            }
                        }
                        else
                        {
                            Trace.WriteLine("HKLM registry key does not exist");
                        }
                    }
                }
                catch (Exception regEx)
                {
                    Trace.WriteLine($"Error checking registry: {regEx.Message}");
                    // Continue with other checks
                }

                // Check 3: Check for App.config setting (config override)
                string devModeDisabledSetting = ConfigurationManager.AppSettings["DevModeDisabled"];
                Trace.WriteLine($"App.config DevModeDisabled setting: '{devModeDisabledSetting}'");
                if (!string.IsNullOrEmpty(devModeDisabledSetting) &&
                    (devModeDisabledSetting.ToLower() == "true" || devModeDisabledSetting == "1"))
                {
                    Trace.WriteLine("Development mode disabled: DevModeDisabled app setting is true");
                    return false;
                }

                // STEP 2: Now check for enabling mechanisms
                Trace.WriteLine("No disable mechanisms found - checking for enable mechanisms...");

                // Method 1: Check for a development mode file in the application directory
                string devModeFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "dev_mode.txt");
                Trace.WriteLine($"Checking for dev_mode.txt file at: {devModeFilePath}");
                if (File.Exists(devModeFilePath))
                {
                    string content = File.ReadAllText(devModeFilePath).Trim();
                    Trace.WriteLine($"dev_mode.txt file exists with content: '{content}'");
                    bool enabled = content.ToLower() == "true";
                    Trace.WriteLine($"Development mode enabled via dev_mode.txt: {enabled}");
                    return enabled;
                }
                else
                {
                    Trace.WriteLine("dev_mode.txt file does not exist");
                }

                // Method 2: Check for an environment variable
                string envVar = Environment.GetEnvironmentVariable("POS_DEVELOPMENT_MODE");
                Trace.WriteLine($"Environment variable POS_DEVELOPMENT_MODE: '{envVar}'");
                if (!string.IsNullOrEmpty(envVar) && (envVar.ToLower() == "true" || envVar == "1"))
                {
                    Trace.WriteLine("Development mode enabled via environment variable");
                    return true;
                }

                // Method 3: Check debug compilation (only if not disabled by config)
                #if DEBUG
                Trace.WriteLine("DEBUG build detected - development mode enabled");
                Trace.WriteLine($"Final IsDevModeEnabled result: TRUE (DEBUG build)");
                Trace.WriteLine("=== END APP.XAML.CS DEVELOPMENT MODE DETECTION ===");
                return true;
                #else
                Trace.WriteLine("RELEASE build detected - development mode disabled");
                Trace.WriteLine($"Final IsDevModeEnabled result: FALSE (RELEASE build)");
                Trace.WriteLine("=== END APP.XAML.CS DEVELOPMENT MODE DETECTION ===");
                return false;
                #endif
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"Error in IsDevModeEnabled: {ex.Message}");
                Trace.WriteLine($"Stack trace: {ex.StackTrace}");
                return false;
            }
            finally
            {
                Trace.WriteLine("=== END DEVELOPMENT MODE DETECTION ===");
            }
        }

        // New method to show the login window
        private void ShowLoginWindow()
        {
            var loginWindow = new LoginWindow();

            // Set correct flow direction based on language
            string language = ConfigurationManager.AppSettings["Language"] ?? "en";
            loginWindow.FlowDirection = language == "ar" ?
                FlowDirection.RightToLeft : FlowDirection.LeftToRight;

            // Apply performance optimizations to login window
            PerformanceOptimizer.OptimizeElement(loginWindow);

            loginWindow.Show();
        }

        // New method to ensure admin user exists
        private bool EnsureAdminUserExists(IDatabaseService dbService)
        {
            try
            {
                // Check if admin user exists
                var adminUser = dbService.AuthenticateUser("admin", "admin123");

                if (adminUser != null)
                {
                    Trace.WriteLine("Admin user already exists and credentials are valid");
                    return true;
                }

                // Get database path
                string dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "pos.db");
                var settingsService = new SettingsService();
                string configuredPath = settingsService.GetSetting("DatabaseLocation");
                if (!string.IsNullOrEmpty(configuredPath))
                {
                    dbPath = configuredPath;
                }

                string connectionString = $"Data Source={dbPath}";

                // Try to fetch users by username regardless of password
                using var connection = new SqliteConnection(connectionString);
                connection.Open();
                var command = connection.CreateCommand();
                command.CommandText = "SELECT Id FROM Users WHERE Username = @Username";
                command.Parameters.AddWithValue("@Username", "admin");

                var existingId = command.ExecuteScalar();

                if (existingId != null)
                {
                    // Admin user exists but password is different, update the password
                    Trace.WriteLine("Admin user exists but password is incorrect. Updating password for development mode.");

                    var updateCommand = connection.CreateCommand();
                    updateCommand.CommandText = "UPDATE Users SET Password = @Password WHERE Username = @Username";
                    updateCommand.Parameters.AddWithValue("@Username", "admin");
                    updateCommand.Parameters.AddWithValue("@Password", BCrypt.Net.BCrypt.HashPassword("admin123"));
                    updateCommand.ExecuteNonQuery();

                    return true;
                }

                // Admin user doesn't exist, create it
                Trace.WriteLine("Creating admin user for development mode");

                // Get Admin role ID
                var roleCommand = connection.CreateCommand();
                roleCommand.CommandText = "SELECT Id FROM Roles WHERE Name = 'Admin'";
                var roleId = roleCommand.ExecuteScalar();

                if (roleId == null)
                {
                    Trace.WriteLine("Admin role not found, creating it");
                    roleCommand = connection.CreateCommand();
                    roleCommand.CommandText = @"INSERT INTO Roles (Name, Description, IsActive, CreatedAt)
                                              VALUES ('Admin', 'System Administrator', 1, @Now)";
                    roleCommand.Parameters.AddWithValue("@Now", DateTime.Now.ToString("o"));
                    roleCommand.ExecuteNonQuery();

                    roleCommand = connection.CreateCommand();
                    roleCommand.CommandText = "SELECT Id FROM Roles WHERE Name = 'Admin'";
                    roleId = roleCommand.ExecuteScalar();
                }

                // Create admin user
                var insertCommand = connection.CreateCommand();
                insertCommand.CommandText = @"
                    INSERT INTO Users (
                        Username, Password, FirstName, LastName,
                        Email, Phone, RoleId, IsActive,
                        CreatedAt, UpdatedAt
                    ) VALUES (
                        @Username, @Password, @FirstName, @LastName,
                        @Email, @Phone, @RoleId, @IsActive,
                        @CreatedAt, @UpdatedAt
                    )";

                var now = DateTime.Now.ToString("o");
                insertCommand.Parameters.AddWithValue("@Username", "admin");
                insertCommand.Parameters.AddWithValue("@Password", BCrypt.Net.BCrypt.HashPassword("admin123"));
                insertCommand.Parameters.AddWithValue("@FirstName", "System");
                insertCommand.Parameters.AddWithValue("@LastName", "Administrator");
                insertCommand.Parameters.AddWithValue("@Email", "<EMAIL>");
                insertCommand.Parameters.AddWithValue("@Phone", "");
                insertCommand.Parameters.AddWithValue("@RoleId", roleId);
                insertCommand.Parameters.AddWithValue("@IsActive", 1);
                insertCommand.Parameters.AddWithValue("@CreatedAt", now);
                insertCommand.Parameters.AddWithValue("@UpdatedAt", now);

                insertCommand.ExecuteNonQuery();

                // Get the new user's ID
                var idCommand = connection.CreateCommand();
                idCommand.CommandText = "SELECT Id FROM Users WHERE Username = @Username";
                idCommand.Parameters.AddWithValue("@Username", "admin");
                var userId = idCommand.ExecuteScalar();

                // Create permissions for the admin user
                var permissionCommand = connection.CreateCommand();
                permissionCommand.CommandText = @"
                    INSERT OR REPLACE INTO UserPermissions (
                        UserId, CanCreateSales, CanVoidSales, CanApplyDiscount,
                        CanViewSalesHistory, CanManageProducts, CanManageCategories,
                        CanViewInventory, CanAdjustInventory, CanManageExpenses,
                        CanManageCashDrawer, CanViewReports, CanManagePrices,
                        CanManageCustomers, CanManageSuppliers, CanManageUsers,
                        CanManageRoles, CanAccessSettings, CanViewLogs,
                        CreatedAt, UpdatedAt
                    ) VALUES (
                        @UserId, 1, 1, 1,
                        1, 1, 1,
                        1, 1, 1,
                        1, 1, 1,
                        1, 1, 1,
                        1, 1, 1,
                        @CreatedAt, @UpdatedAt
                    )";

                permissionCommand.Parameters.AddWithValue("@UserId", userId);
                permissionCommand.Parameters.AddWithValue("@CreatedAt", now);
                permissionCommand.Parameters.AddWithValue("@UpdatedAt", now);
                permissionCommand.ExecuteNonQuery();

                Trace.WriteLine("Admin user created successfully for development mode");
                return true;
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"Error ensuring admin user exists: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Trace.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return false;
            }
        }

        private void ActivateExistingInstance()
        {
            try
            {
                // Find the main window of the existing instance
                var currentProcess = Process.GetCurrentProcess();
                var processes = Process.GetProcessesByName(currentProcess.ProcessName);

                foreach (var process in processes)
                {
                    if (process.Id != currentProcess.Id)
                    {
                        // Found the existing instance
                        NativeMethods.SetForegroundWindow(process.MainWindowHandle);

                        // Post a custom message to the existing window to trigger user switch
                        NativeMethods.PostMessage(process.MainWindowHandle, NativeMethods.WM_SWITCHUSER, IntPtr.Zero, IntPtr.Zero);
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Trace.WriteLine($"Error activating existing instance: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ ENHANCED DEBUG FILE LOGGING: Sets up comprehensive debug logging to capture all output
        /// </summary>
        private void SetupEnhancedDebugLogging()
        {
            try
            {
                // Create logs directory
                var logsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
                Directory.CreateDirectory(logsDirectory);

                // Create timestamped debug file for this session
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
                var debugFilePath = Path.Combine(logsDirectory, $"debug_{timestamp}.log");
                var performanceFilePath = Path.Combine(logsDirectory, $"performance_{timestamp}.log");

                // Set up multiple trace listeners for comprehensive logging

                // 1. Main debug file listener
                var debugListener = new TextWriterTraceListener(debugFilePath, "DebugFileLogger");
                Trace.Listeners.Add(debugListener);

                // 2. Performance-specific listener
                var performanceListener = new TextWriterTraceListener(performanceFilePath, "PerformanceLogger");
                Trace.Listeners.Add(performanceListener);

                // 3. Console listener for immediate feedback
                var consoleListener = new ConsoleTraceListener();
                Trace.Listeners.Add(consoleListener);

                // Enable auto-flush for immediate writing
                Trace.AutoFlush = true;

                // Note: Debug.WriteLine automatically uses the same listeners as Trace.WriteLine

                // Log the setup
                Trace.WriteLine("=".PadRight(80, '='));
                Trace.WriteLine($"ENHANCED DEBUG LOGGING INITIALIZED - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                Trace.WriteLine($"Debug Log: {debugFilePath}");
                Trace.WriteLine($"Performance Log: {performanceFilePath}");
                Trace.WriteLine("=".PadRight(80, '='));

                // Create a latest.log symlink for easy access
                var latestDebugPath = Path.Combine(logsDirectory, "latest_debug.log");
                var latestPerformancePath = Path.Combine(logsDirectory, "latest_performance.log");

                try
                {
                    if (File.Exists(latestDebugPath)) File.Delete(latestDebugPath);
                    if (File.Exists(latestPerformancePath)) File.Delete(latestPerformancePath);

                    File.Copy(debugFilePath, latestDebugPath);
                    File.Copy(performanceFilePath, latestPerformancePath);
                }
                catch
                {
                    // Ignore symlink creation errors
                }
            }
            catch (Exception ex)
            {
                // Fallback to basic logging if enhanced setup fails
                var basicLogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "debug.log");
                var basicListener = new TextWriterTraceListener(basicLogPath, "BasicFileLogger");
                Trace.Listeners.Add(basicListener);
                Trace.AutoFlush = true;

                Trace.WriteLine($"Enhanced debug logging setup failed, using basic logging: {ex.Message}");
            }
        }

        // Add NativeMethods class for Win32 API calls
        private static class NativeMethods
        {
            public const int WM_SWITCHUSER = 0x0400 + 1; // Custom window message

            [System.Runtime.InteropServices.DllImport("user32.dll")]
            public static extern bool SetForegroundWindow(IntPtr hWnd);

            [System.Runtime.InteropServices.DllImport("user32.dll")]
            public static extern bool PostMessage(IntPtr hWnd, int Msg, IntPtr wParam, IntPtr lParam);
        }
    }
}

