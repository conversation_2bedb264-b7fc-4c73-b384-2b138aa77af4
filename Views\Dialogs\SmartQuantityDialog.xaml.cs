using MaterialDesignThemes.Wpf;
using POSSystem.Models;
using POSSystem.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace POSSystem.Views.Dialogs
{
    /// <summary>
    /// Smart quantity selection dialog that suggests optimal quantities based on bulk pricing.
    /// Provides an intuitive interface for customers to see potential savings and make informed decisions.
    /// </summary>
    public partial class SmartQuantityDialog : UserControl
    {
        private readonly BulkPricingService _bulkPricingService;
        private Product _product;
        private decimal _selectedQuantity = 1;
        private List<QuantitySuggestion> _suggestions;

        // Events for window-based dialog handling
        public event Action<SmartQuantityResult> AddToCartClicked;
        public event Action CancelClicked;

        public decimal SelectedQuantity
        {
            get => _selectedQuantity;
            set
            {
                _selectedQuantity = value;
                UpdatePricingDisplay();
                UpdateSuggestions();
            }
        }

        public Product Product
        {
            get => _product;
            set
            {
                _product = value;

                // Ensure controls are loaded before accessing them
                if (ProductNameText != null)
                    ProductNameText.Text = _product?.Name ?? "Unknown Product";
                if (UnitText != null)
                    UnitText.Text = _product?.UnitOfMeasure?.Name ?? "units";

                UpdatePricingDisplay();
                UpdateSuggestions();
            }
        }

        public SmartQuantityDialog(Product product, decimal initialQuantity = 1)
        {
            InitializeComponent();

            _bulkPricingService = new BulkPricingService();
            Product = product;
            SelectedQuantity = initialQuantity;

            // Ensure control is loaded before setting text
            if (txtQuantity != null)
                txtQuantity.Text = initialQuantity.ToString("0.###");
        }

        private void UpdatePricingDisplay()
        {
            if (_product == null) return;

            // Ensure controls are loaded before accessing them
            if (CurrentPricingText == null || CurrentSavingsText == null || CurrentTotalText == null)
                return;

            try
            {
                var pricingResult = _bulkPricingService.CalculateBestPricing(_product, SelectedQuantity);

                // Update current pricing display
                if (pricingResult.AppliedTier != null)
                {
                    CurrentPricingText.Text = $"{pricingResult.EffectiveUnitPrice:C2} each ({pricingResult.AppliedTier.GetDisplayText()})";
                    CurrentSavingsText.Text = pricingResult.SavingsDisplay;
                    CurrentSavingsText.Visibility = Visibility.Visible;
                }
                else
                {
                    CurrentPricingText.Text = $"{_product.SellingPrice:C2} each (Regular price)";
                    CurrentSavingsText.Text = "";
                    CurrentSavingsText.Visibility = Visibility.Collapsed;
                }

                CurrentTotalText.Text = pricingResult.TotalPrice.ToString("C2");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SMART_QUANTITY] Error updating pricing display: {ex.Message}");
                CurrentPricingText.Text = $"{_product.SellingPrice:C2} each";
                CurrentTotalText.Text = (_product.SellingPrice * SelectedQuantity).ToString("C2");
                CurrentSavingsText.Visibility = Visibility.Collapsed;
            }
        }

        private void UpdateSuggestions()
        {
            if (_product == null || !_product.HasBulkPricing)
            {
                SuggestionsPanel.ItemsSource = null;
                return;
            }

            try
            {
                _suggestions = _bulkPricingService.GetQuantitySuggestions(_product, SelectedQuantity, 5);
                SuggestionsPanel.ItemsSource = _suggestions;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SMART_QUANTITY] Error updating suggestions: {ex.Message}");
                SuggestionsPanel.ItemsSource = null;
            }
        }

        private void DecimalValidation_PreviewTextInput(object sender, TextCompositionEventArgs e)
        {
            var textBox = sender as TextBox;
            var fullText = textBox.Text.Insert(textBox.SelectionStart, e.Text);
            
            // Allow decimal numbers with up to 3 decimal places
            e.Handled = !Regex.IsMatch(fullText, @"^\d*\.?\d{0,3}$");
        }

        private void Quantity_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(txtQuantity.Text, out decimal quantity) && quantity > 0)
            {
                _selectedQuantity = quantity;
                UpdatePricingDisplay();
                UpdateSuggestions();
            }
        }

        private void Suggestion_Click(object sender, MouseButtonEventArgs e)
        {
            var border = sender as Border;
            var suggestion = border?.Tag as QuantitySuggestion;
            
            if (suggestion != null)
            {
                SelectedQuantity = suggestion.SuggestedQuantity;
                txtQuantity.Text = suggestion.SuggestedQuantity.ToString("0.###");
            }
        }

        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            // Try DialogHost first, then fallback to event
            try
            {
                DialogHost.CloseDialogCommand.Execute(false, this);
            }
            catch
            {
                // If DialogHost fails, trigger the event for window-based dialog
                CancelClicked?.Invoke();
            }
        }

        private void AddToCart_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateQuantity())
            {
                var result = new SmartQuantityResult
                {
                    Product = _product,
                    Quantity = SelectedQuantity,
                    Success = true
                };

                // Try DialogHost first, then fallback to event
                try
                {
                    DialogHost.CloseDialogCommand.Execute(result, this);
                }
                catch
                {
                    // If DialogHost fails, trigger the event for window-based dialog
                    AddToCartClicked?.Invoke(result);
                }
            }
        }

        private bool ValidateQuantity()
        {
            if (!decimal.TryParse(txtQuantity.Text, out decimal quantity) || quantity <= 0)
            {
                MessageBox.Show(
                    "Please enter a valid quantity greater than 0.",
                    "Invalid Quantity",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
                txtQuantity.Focus();
                return false;
            }

            // Check stock availability
            if (_product.Type != ProductType.Service && quantity > _product.GetTotalStockDecimal())
            {
                var result = MessageBox.Show(
                    $"Only {_product.GetTotalStockDecimal():0.###} {_product.UnitOfMeasure?.Name ?? "units"} available in stock.\n\nDo you want to add the available quantity instead?",
                    "Insufficient Stock",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    SelectedQuantity = _product.GetTotalStockDecimal();
                    txtQuantity.Text = SelectedQuantity.ToString("0.###");
                    return true;
                }
                return false;
            }

            SelectedQuantity = quantity;
            return true;
        }

        /// <summary>
        /// Shows the smart quantity dialog and returns the selected quantity.
        /// </summary>
        /// <param name="product">Product to select quantity for</param>
        /// <param name="initialQuantity">Initial quantity to display</param>
        /// <param name="dialogIdentifier">Dialog host identifier</param>
        /// <returns>Smart quantity result with selected product and quantity</returns>
        public static async Task<SmartQuantityResult> ShowAsync(Product product, decimal initialQuantity = 1, string dialogIdentifier = "RootDialog")
        {
            var dialog = new SmartQuantityDialog(product, initialQuantity);

            try
            {
                // First try using the provided identifier
                var result = await DialogHost.Show(dialog, dialogIdentifier);

                if (result is SmartQuantityResult smartResult && smartResult.Success)
                {
                    return smartResult;
                }

                return new SmartQuantityResult { Success = false };
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("already open"))
            {
                // If DialogHost is busy, try opening as a regular WPF window instead
                System.Diagnostics.Debug.WriteLine("[SMART_QUANTITY_DIALOG] DialogHost busy, opening as window");
                return await ShowAsWindowAsync(dialog);
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost") || ex.Message.Contains("Identifier"))
            {
                // If DialogHost identifier doesn't exist, try opening as a regular WPF window
                System.Diagnostics.Debug.WriteLine("[SMART_QUANTITY_DIALOG] DialogHost not found, opening as window");
                return await ShowAsWindowAsync(dialog);
            }
            catch (Exception ex)
            {
                // For any other exception, try opening as a regular WPF window
                System.Diagnostics.Debug.WriteLine($"[SMART_QUANTITY_DIALOG] Unexpected error: {ex.Message}, opening as window");
                return await ShowAsWindowAsync(dialog);
            }
        }

        private static async Task<SmartQuantityResult> ShowAsWindowAsync(SmartQuantityDialog dialog)
        {
            return await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                SmartQuantityResult dialogResult = null;

                // Create a window to host the dialog content
                var window = new Window
                {
                    Title = "Smart Quantity Selection",
                    Content = dialog,
                    SizeToContent = SizeToContent.WidthAndHeight,
                    ResizeMode = ResizeMode.NoResize,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = System.Windows.Application.Current.MainWindow,
                    ShowInTaskbar = false
                };

                // Handle the dialog result when AddToCart is clicked
                dialog.AddToCartClicked += (result) =>
                {
                    dialogResult = result;
                    window.DialogResult = true;
                    window.Close();
                };

                // Handle cancel
                dialog.CancelClicked += () =>
                {
                    dialogResult = new SmartQuantityResult { Success = false };
                    window.DialogResult = false;
                    window.Close();
                };

                // Show the window as a modal dialog
                window.ShowDialog();

                return dialogResult ?? new SmartQuantityResult { Success = false };
            });
        }
    }

    /// <summary>
    /// Result of the smart quantity selection dialog.
    /// </summary>
    public class SmartQuantityResult
    {
        public Product Product { get; set; }
        public decimal Quantity { get; set; }
        public bool Success { get; set; }
    }
}
