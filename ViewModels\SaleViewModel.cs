using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using POSSystem.Models;
using POSSystem.Services;
using POSSystem.Services.Customers;
using POSSystem.Services.Interfaces;
using POSSystem.Views;
using Microsoft.EntityFrameworkCore;
using POSSystem.Data;
using System.Diagnostics;
using System.Linq.Expressions;
using MaterialDesignThemes.Wpf;
using System.Timers;
using CommunityToolkit.Mvvm.Input;
using System.Windows.Media;
using POSSystem.Helpers;
using POSSystem.Commands;
using static POSSystem.Helpers.UIThreadProtection;
using System.Threading;
using POSSystem.Helpers;
using POSSystem.Services.InventoryManagement;


namespace POSSystem.ViewModels
{
    public class SaleViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly IDatabaseService _dbService;
        private readonly RepositoryServiceAdapter _repositoryAdapter;
        private readonly IAuthenticationService _authService;
        private readonly IStockService _stockService;
        private readonly ISettingsService _settingsService;
        private readonly IThemeService _themeService;
        private readonly ICustomerService _customerService;
        private readonly ICashDrawerService _cashDrawerService;
        private readonly ISoundService _soundService;
        private readonly IDiscountService _discountService;
        private readonly IInvoiceNumberService _invoiceNumberService;
        public User CurrentUser
        {
            get
            {
                var user = _authService.CurrentUser;
                // ✅ PERFORMANCE FIX: Removed frequent debug logging that was impacting frame rates
                #if DEBUG && VERBOSE_LOGGING
                System.Diagnostics.Debug.WriteLine($"[SALEVIEWMODEL] CurrentUser accessed: {user?.Username ?? "NULL"} (ID: {user?.Id ?? -1})");
                #endif
                return user;
            }
        }
        public IDiscountService DiscountService => _discountService;

        private ICommand _removeFromCartCommand;
        private ICommand _createNewCartCommand;
        private ICommand _lookupCustomerCommand;
        private ICommand _redeemPointsCommand;
        private ICommand _increaseQuantityCommand;
        private ICommand _decreaseQuantityCommand;
        private ICommand _toggleFavoriteCommand;
        private ICommand _showFavoritesCommand;
        private ICommand _addToCartCommand;
        private ICommand _searchCommand;
        private ICommand _clearSearchCommand;
        private ICommand _processPaymentCommand;
        private ICommand _refreshProductsCommand;

        public ICommand RemoveFromCartCommand => _removeFromCartCommand ??= new RelayCommand<int>(RemoveFromCart);
        public ICommand CreateNewCartCommand => _createNewCartCommand ??= new RelayCommand(_ => CreateNewCart());
        public ICommand LookupCustomerCommand => _lookupCustomerCommand ??= new POSSystem.Helpers.AsyncRelayCommand(async () => await SelectCustomerAsync());
        public ICommand RedeemPointsCommand => _redeemPointsCommand ??= new RelayCommand(
            _ => RedeemPoints(),
            _ => CanRedeemPoints);
        public ICommand IncreaseQuantityCommand => _increaseQuantityCommand ??= new RelayCommand(_ => IncreaseSelectedQuantity());
        public ICommand DecreaseQuantityCommand => _decreaseQuantityCommand ??= new RelayCommand(_ => DecreaseSelectedQuantity());
        public ICommand ToggleFavoriteCommand => _toggleFavoriteCommand ??= new POSSystem.Helpers.AsyncRelayCommand<Product>(async (product) =>
        {
            if (product == null) return;
            await ToggleFavorite(product);
        });
        public ICommand ShowFavoritesCommand => _showFavoritesCommand ??= new RelayCommand(_ => ToggleShowFavorites());
        public ICommand AddToCartCommand => _addToCartCommand ??= new RelayCommand<Product>(product =>
        {
            if (product == null) return;

            // Weight-based products: still prompt for weight; then we'll route to batch-aware add inside the dialog accept path
            if (product.IsWeightBased)
            {
                ShowWeightBasedProductDialog(product);
                return;
            }

            // Unit-based products
            if (product.TrackBatches)
            {
                AllocateIntoCartByBatches(product, 1m);
            }
            else
            {
                AddToCart(product, 1m);
            }
        });
        public ICommand SearchCommand => _searchCommand ??= new RelayCommand(_ => FocusSearchBox());
        public ICommand ClearSearchCommand => _clearSearchCommand ??= new RelayCommand(_ => ClearSearch());
        public ICommand ProcessPaymentCommand => _processPaymentCommand ??= new RelayCommand(_ =>
        {
            // Payment processing is now handled by the view layer
            POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] ProcessPaymentCommand executed - payment should be handled by view");
        });
        public ICommand RefreshProductsCommand => _refreshProductsCommand ??= new POSSystem.Helpers.AsyncRelayCommand(async () => await RefreshProducts());

        private ObservableCollection<Cart> carts;
        private Cart activeCart;

        private ObservableCollection<CartItem> _cartItems;

        public ObservableCollection<Cart> Carts
        {
            get => carts;
            set
            {
                if (carts == value) return;
                carts = value;
                OnPropertyChanged();
            }
        }

        public Cart ActiveCart
        {
            get => activeCart;
            set
            {
                if (activeCart == value) return;

                // Update all carts' IsActive status
                if (Carts != null)
                {
                    foreach (var cart in Carts)
                    {
                        cart.IsActive = (cart == value);
                    }
                }

                activeCart = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(CartItems));
            }
        }

        public ObservableCollection<CartItem> CartItems
        {
            get
            {
                if (ActiveCart != null && _cartItems != ActiveCart.Items)
                {
                    _cartItems = ActiveCart.Items;
                    _cartItems.CollectionChanged += CartItems_CollectionChanged;
                }
                return _cartItems;
            }
        }

        public List<Product> Products { get; set; }

        private decimal subtotal;
        private decimal discountAmount;
        private decimal taxAmount;
        private decimal grandTotal;

        public decimal Subtotal
        {
            get => subtotal;
            private set
            {
                subtotal = value;
                OnPropertyChanged();
            }
        }

        public decimal DiscountAmount
        {
            get => discountAmount;
            set
            {
                discountAmount = value;
                OnPropertyChanged();
                CalculateTotals();
            }
        }

        public decimal TaxRate { get; set; } = 0.0m;

        public decimal TaxAmount
        {
            get => taxAmount;
            private set
            {
                taxAmount = value;
                OnPropertyChanged();
            }
        }

        public decimal GrandTotal
        {
            get => grandTotal;
            private set
            {
                grandTotal = value;
                OnPropertyChanged();
            }
        }

        private Customer _selectedCustomer;

        public Customer SelectedCustomer
        {
            get => _selectedCustomer;
            set
            {
                if (_selectedCustomer != value)
                {
                    _selectedCustomer = value;
                    OnPropertyChanged();
                    UpdateLoyaltyInfo();
                }
            }
        }

        private decimal _earnedPoints;

        public decimal EarnedPoints
        {
            get => _earnedPoints;
            set
            {
                _earnedPoints = value;
                OnPropertyChanged();
            }
        }

        public decimal TotalItems => ActiveCart?.Items.Sum(i => i.Quantity) ?? 0;

        private ObservableCollection<Product> _filteredProducts = new ObservableCollection<Product>();
        private ObservableCollection<Product> _allProducts;
        private ObservableCollection<Category> _categories;
        private ObservableCollection<Customer> _customers;

        // ✅ PERFORMANCE: Collection size limits to prevent memory bloat
        private const int MAX_FILTERED_PRODUCTS = 500;
        private const int MAX_ALL_PRODUCTS = 1000;
        private Cart _currentCart;
        private string _selectedPaymentMethod;
        private bool _hasLoyaltyCustomer;
        private decimal _loyaltyPointsEarned;
        private decimal _customerLoyaltyPoints;
        private string _loyaltyCardNumber;
        private bool _canRedeemPoints;

        private Category _selectedCategory;

        public Category SelectedCategory
        {
            get => _selectedCategory;
            set
            {
                if (_selectedCategory == value) return;
                _selectedCategory = value;
                OnPropertyChanged();

                // Reset the product list with the new category filter
                if (value != null)
                {
                    FilterProductsByCategory(value.Id);
                }
                else
                {
                    // When no category is selected, only auto-load if we're not in a special state
                    // (like showing favorites or popular items)
                    if (!ShowingFavorites && !ShowingPopularItems)
                    {
                        _currentPage = 0;
                        _hasMoreItems = true;
                        FilteredProducts.Clear();
                        // Avoid clearing AllProducts to reduce UI churn; LoadMoreProducts will diff/apply
                        _searchTerms.Clear();
                        _ = LoadMoreProducts();
                    }
                }
            }
        }

        public ObservableCollection<Product> FilteredProducts
        {
            get => _filteredProducts;
            set
            {
                _filteredProducts = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Product> AllProducts
        {
            get => _allProducts;
            set
            {
                _allProducts = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Category> Categories
        {
            get => _categories;
            set
            {
                _categories = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<Customer> Customers
        {
            get => _customers;
            set
            {
                _customers = value;
                OnPropertyChanged();
            }
        }

        public Cart CurrentCart
        {
            get => _currentCart;
            set
            {
                _currentCart = value;
                OnPropertyChanged();
            }
        }

        public ObservableCollection<string> PaymentMethods { get; }

        public string SelectedPaymentMethod
        {
            get => _selectedPaymentMethod;
            set
            {
                _selectedPaymentMethod = value;
                OnPropertyChanged();
            }
        }

        public bool HasLoyaltyCustomer
        {
            get => _hasLoyaltyCustomer;
            set
            {
                _hasLoyaltyCustomer = value;
                OnPropertyChanged();
            }
        }

        public decimal LoyaltyPointsEarned
        {
            get => _loyaltyPointsEarned;
            set
            {
                _loyaltyPointsEarned = value;
                OnPropertyChanged();
            }
        }

        public decimal CustomerLoyaltyPoints
        {
            get => _customerLoyaltyPoints;
            set
            {
                _customerLoyaltyPoints = value;
                OnPropertyChanged();
            }
        }

        public string LoyaltyCardNumber
        {
            get => _loyaltyCardNumber;
            set
            {
                _loyaltyCardNumber = value;
                OnPropertyChanged();
            }
        }

        public bool CanRedeemPoints
        {
            get => _canRedeemPoints;
            set
            {
                _canRedeemPoints = value;
                OnPropertyChanged();
            }
        }

        /// <summary>
        /// Allocate the requested quantity into the cart according to FIFO batches, respecting per-batch prices.
        /// Splits/merges cart lines by price as needed and fires BatchBoundaryCrossed when price changes.
        /// </summary>
        private void AllocateIntoCartByBatches(Product product, decimal quantity)
        {
            if (product == null || quantity <= 0) return;

            // Ensure cart exists
            if (CurrentCart == null)
            {
                CreateNewCart();
                if (CurrentCart == null) return;
            }

            try
            {
                var rawBatches = _dbService.GetBatchesForProduct(product.Id)
                    .Where(b => b.Quantity > 0)
                    .OrderBy(b => b.CreatedAt)
                    .ThenBy(b => b.Id)
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Add via FIFO allocator - product {product.Id}, qty {quantity}. Batches={rawBatches.Count}");
                foreach (var b in rawBatches)
                {
                    System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Batch {b.BatchNumber} (Id={b.Id}) Qty={b.Quantity}, Price={b.SellingPrice}, CreatedAt={b.CreatedAt:O}");
                }

                if (rawBatches == null || rawBatches.Count == 0)
                {
                    // No batch stock available: block add just like the '+' path would
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        string unit = product.IsWeightBased ? (product.UnitOfMeasure?.Abbreviation ?? "units") : "items";
                        MessageBox.Show($"This product is out of stock (0 {unit} available).", "Out of Stock",
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                    return;
                }

                // Consistent stock validation with '+' button behavior
                decimal availableStock = rawBatches.Sum(b => b.Quantity);
                // Fallback: if batches not loaded properly but product shows stock, use StockQuantity
                if (availableStock == 0 && product.StockQuantity > 0)
                {
                    availableStock = product.StockQuantity;
                    System.Diagnostics.Debug.WriteLine($"[STOCK_VALIDATION] Batch-tracked product {product.Name}: Batches not loaded properly, using StockQuantity = {availableStock}");
                }

                var existingInCart = CurrentCart.Items.Where(i => i.Product.Id == product.Id).Sum(i => i.Quantity);
                var totalAfterAdd = existingInCart + quantity;
                if (totalAfterAdd > availableStock)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        string unit = product.IsWeightBased ? (product.UnitOfMeasure?.Abbreviation ?? "units") : "items";
                        MessageBox.Show($"Cannot add more. Only {availableStock:F3} {unit} available in stock.",
                            "Stock Limit Reached", MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                    return;
                }

                // Compute virtual availability by subtracting quantities already in cart at the same price (FIFO per price)
                var inCartByPrice = CurrentCart.Items
                    .Where(i => i.Product.Id == product.Id)
                    .GroupBy(i => i.UnitPrice)
                    .ToDictionary(g => g.Key, g => g.Sum(i => i.Quantity));

                var effectiveBatches = new List<(BatchStock batch, decimal price, decimal available)>();
                foreach (var b in rawBatches)
                {
                    var price = b.SellingPrice > 0 ? b.SellingPrice : product.SellingPrice;
                    decimal reservedAtPrice = 0;
                    if (inCartByPrice.TryGetValue(price, out var reserved))
                    {
                        reservedAtPrice = Math.Min(reserved, b.Quantity);
                        inCartByPrice[price] = reserved - reservedAtPrice;
                    }
                    var available = Math.Max(0, b.Quantity - reservedAtPrice);
                    effectiveBatches.Add((b, price, available));
                    System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] EffectiveAvail for batch {b.BatchNumber}: {available} at price {price} (reserved {reservedAtPrice})");
                }

                decimal remaining = quantity;
                decimal? firstPrice = null;
                BatchStock boundaryBatch = null;

                foreach (var eb in effectiveBatches)
                {
                    if (remaining <= 0) break;
                    var take = Math.Min(remaining, eb.available);
                    if (take <= 0) continue;

                    var price = eb.price;
                    if (firstPrice == null) firstPrice = price;
                    if (firstPrice != null && Math.Abs(price - firstPrice.Value) > 0.0001m && boundaryBatch == null)
                    {
                        boundaryBatch = eb.batch;
                    }

                    System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Allocate Add: taking {take} from {eb.batch.BatchNumber} at {price}");

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        var line = CurrentCart.Items.FirstOrDefault(i => i.Product.Id == product.Id && Math.Abs(i.UnitPrice - price) < 0.0001m);
                        if (line != null)
                        {
                            line.Quantity += take;
                            SelectedCartItem = line;
                        }
                        else
                        {
                            var newLine = new CartItem
                            {
                                Product = product,
                                Quantity = take,
                                UnitPrice = price,
                                BatchUnitPrice = price
                            };
                            CurrentCart.Items.Add(newLine);
                            SelectedCartItem = newLine;
                        }
                    });

                    remaining -= take;
                }

                if (boundaryBatch != null)
                {
                    BatchBoundaryCrossed?.Invoke(this, new BatchBoundaryEventArgs(product, boundaryBatch.SellingPrice > 0 ? boundaryBatch.SellingPrice : product.SellingPrice, boundaryBatch.BatchNumber, boundaryBatch.ExpiryDate));
                }

                Application.Current.Dispatcher.Invoke(() =>
                {
                    _soundService.PlayCartAddSound();
                    CalculateTotals();
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Error in AllocateIntoCartByBatches: {ex.Message}");
                // Fallback: simple add
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var item = CurrentCart.Items.FirstOrDefault(i => i.Product.Id == product.Id);
                    if (item != null) item.Quantity += quantity;
                    else CurrentCart.Items.Add(new CartItem { Product = product, Quantity = quantity, UnitPrice = product.SellingPrice });
                    CalculateTotals();
                });
            }
        }



        private CartItem _selectedCartItem;
        public CartItem SelectedCartItem
        {
            get => _selectedCartItem;
            set
            {
                _selectedCartItem = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(HasSelectedCartItem));
            }
        }

        public bool HasSelectedCartItem => SelectedCartItem != null;
        public bool HasCartItems => ActiveCart?.Items?.Any() ?? false;
        public bool CanCloseCart => Carts?.Count > 0; // Allow closing any cart that exists

        private const int PAGE_SIZE = 15; // Reduced for better initial performance
        private int _currentPage = 0;
        private bool _isLoading;
        private string _currentSearchText;
        private bool _hasMoreItems = true;
        private HashSet<string> _searchTerms = new HashSet<string>();

        private const int POPULAR_ITEMS_COUNT = 20;
        private bool _showingPopularItems = true;

        public bool ShowingPopularItems
        {
            get => _showingPopularItems;
            set
            {
                if (_showingPopularItems != value)
                {
                    _showingPopularItems = value;
                    OnPropertyChanged();
                    if (!value)
                    {
                        _ = RefreshProducts();
                    }
                }
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged();
            }
        }

        private const int SEARCH_DELAY_MS = 300;
        private const int CACHE_SIZE = 100;
        private const int SEARCH_PAGE_SIZE = 20;
        private System.Timers.Timer _searchTimer;
        private CancellationTokenSource _searchCancellation;
        private readonly Dictionary<string, (List<Product> Products, DateTime Timestamp)> _searchCache
            = new Dictionary<string, (List<Product>, DateTime)>();
        private const int CACHE_EXPIRY_MINUTES = 5;
        private string _lastSearchText;
        private bool _isSearching;

        // Add a flag to track barcode processing
        private bool _processingBarcode = false;

        public bool IsSearching
        {
            get => _isSearching;
            private set
            {
                _isSearching = value;
                OnPropertyChanged();
            }
        }

        // Invalidate search caches to avoid stale results after stock changes
        public void InvalidateSearchCaches()
        {
            try
            {
                _searchCache.Clear();
                _lastSearchText = null;
            }
            catch { }
        }

        private string _searchText;
        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText == value) return;
                _searchText = value;
                OnPropertyChanged();

                // IMPORTANT: If input is a barcode (8 or more digits), do not filter or search
                if (!string.IsNullOrEmpty(value) && value.Length >= 8 && value.All(char.IsDigit))
                {
                    Debug.WriteLine($"Skipping search for barcode input: {value}");
                    _processingBarcode = true;
                    return;
                }

                // Add null check to prevent NullReferenceException
                if (_searchTimer != null)
                {
                    _searchTimer.Stop();
                }

                if (_searchCancellation != null)
                {
                    _searchCancellation.Cancel();
                    _searchCancellation.Dispose();
                }
                _searchCancellation = new CancellationTokenSource();

                if (string.IsNullOrWhiteSpace(value))
                {
                    // Only show popular products if we're not in the middle of barcode processing
                    if (!_processingBarcode)
                    {
                        ShowPopularProducts();
                    }
                    // Reset barcode processing flag
                    _processingBarcode = false;
                }
                else
                {
                    // Reset barcode processing flag
                    _processingBarcode = false;

                    // Start search timer
                    IsSearching = true;
                    if (_searchTimer != null)
                    {
                        _searchTimer.Start();
                    }
                    else
                    {
                        // If timer is still null for some reason, execute search directly
                        Task.Run(async () => await SearchProducts());
                    }
                }
            }
        }

        private const int INITIAL_LOAD_SIZE = 20; // Reduced for faster initial load
        private const int BATCH_SIZE = 20; // Reduced for smoother scrolling
        private bool _isLoadingMore = false;
        private int _currentOffset = 0;

        private bool _showingFavorites;
        private ObservableCollection<Product> _favoriteProducts;

        public bool ShowingFavorites
        {
            get => _showingFavorites;
            set
            {
                if (_showingFavorites != value)
                {
                    _showingFavorites = value;
                    OnPropertyChanged();
                }
            }
        }

        public ObservableCollection<Product> FavoriteProducts
        {
            get => _favoriteProducts;
            set
            {
                _favoriteProducts = value;
                OnPropertyChanged();
            }
        }

        public string CurrentSalesLayoutTheme { get; private set; }

        // Invoice Creation Permission
        public bool CanCreateInvoices
        {
            get
            {
                try
                {
                    // ✅ FIX: Use DI container instead of ServiceLocator
                    var permissionsService = App.ServiceProvider?.GetService(typeof(UserPermissionsService)) as UserPermissionsService;
                    return permissionsService?.CanCreateDraftInvoices() == true || permissionsService?.CanCreateFullInvoices() == true;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALEVIEWMODEL] Error checking invoice permissions: {ex.Message}");
                    return false;
                }
            }
        }

        // Sales Navigation Properties
        private bool _isEditMode;
        private Sale _currentLoadedSale;
        private int? _currentSaleId;
        private string _invoiceNumberSearch;
        private string _currentInvoiceNumber;
        private ICommand _loadSaleByInvoiceCommand;
        private ICommand _nextSaleCommand;
        private ICommand _previousSaleCommand;
        private ICommand _exitEditModeCommand;
        private ICommand _saveModifiedSaleCommand;

        public bool IsEditMode
        {
            get => _isEditMode;
            set
            {
                if (_isEditMode != value)
                {
                    _isEditMode = value;
                    OnPropertyChanged();
                }
            }
        }

        public string CurrentInvoiceNumber
        {
            get => _currentInvoiceNumber;
            set
            {
                if (_currentInvoiceNumber != value)
                {
                    _currentInvoiceNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public Sale CurrentLoadedSale
        {
            get => _currentLoadedSale;
            set
            {
                if (_currentLoadedSale != value)
                {
                    _currentLoadedSale = value;
                    _currentSaleId = value?.Id;
                    OnPropertyChanged();
                }
            }
        }

        public string InvoiceNumberSearch
        {
            get => _invoiceNumberSearch;
            set
            {
                if (_invoiceNumberSearch != value)
                {
                    _invoiceNumberSearch = value;
                    OnPropertyChanged();
                }
            }
        }

        public ICommand LoadSaleByInvoiceCommand => _loadSaleByInvoiceCommand ??= new POSSystem.Helpers.AsyncRelayCommand<string>(async (invoiceNumber) => await LoadSaleByInvoice(invoiceNumber));
        public ICommand NextSaleCommand => _nextSaleCommand ??= new POSSystem.Helpers.AsyncRelayCommand(async () => await NavigateToNextSale());
        public ICommand PreviousSaleCommand => _previousSaleCommand ??= new POSSystem.Helpers.AsyncRelayCommand(async () => await NavigateToPreviousSale());
        public ICommand ExitEditModeCommand => _exitEditModeCommand ??= new RelayCommand(_ => ExitEditMode());
        public ICommand SaveModifiedSaleCommand => _saveModifiedSaleCommand ??= new CommunityToolkit.Mvvm.Input.AsyncRelayCommand(async () => await SaveModifiedSale());

        // Add static event for sale completion
        public static event EventHandler SaleCompleted;

        // Add this field after the other cache-related fields in the class
        private readonly Dictionary<string, (Product Product, DateTime Timestamp)> _barcodeCache = new Dictionary<string, (Product, DateTime)>();
        private const int BARCODE_CACHE_EXPIRY_MINUTES = 30;

        // Add static event for stock changes
        public static event EventHandler<ProductStockChangedEventArgs> ProductStockChanged;

        // Add event args class
        public class ProductStockChangedEventArgs : EventArgs
        {
            public int ProductId { get; }
            public decimal NewStockQuantity { get; }

            public ProductStockChangedEventArgs(int productId, decimal newStockQuantity)
            {
                ProductId = productId;
                NewStockQuantity = newStockQuantity;
            }
        }

        // Event raised when quantity increase crosses a FIFO batch boundary (for user notification)
        public event EventHandler<BatchBoundaryEventArgs> BatchBoundaryCrossed;
        public class BatchBoundaryEventArgs : EventArgs
        {
            public Product Product { get; }
            public decimal NewBatchUnitPrice { get; }
            public string BatchNumber { get; }
            public DateTime? ExpiryDate { get; }
            public BatchBoundaryEventArgs(Product product, decimal newBatchUnitPrice, string batchNumber, DateTime? expiryDate)
            {
                Product = product;
                NewBatchUnitPrice = newBatchUnitPrice;
                BatchNumber = batchNumber;
                ExpiryDate = expiryDate;
            }
        }


        // Public static helper to raise ProductStockChanged from other components
        public static void NotifyProductStockChanged(int productId, decimal newStockQuantity, object sender = null)
        {
            try
            {
                ProductStockChanged?.Invoke(sender ?? typeof(SaleViewModel), new ProductStockChangedEventArgs(productId, newStockQuantity));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALE_VM] Error notifying ProductStockChanged: {ex.Message}");
            }
        }


        // Constructor for dependency injection with repository support
        public SaleViewModel(
            IDatabaseService dbService,
            IAuthenticationService authService,
            ISettingsService settingsService,
            IThemeService themeService,
            ICustomerService customerService,
            ICashDrawerService cashDrawerService,
            ISoundService soundService,
            IDiscountService discountService,
            IInvoiceNumberService invoiceNumberService,
            RepositoryServiceAdapter repositoryAdapter = null,
            IStockService stockService = null)
        {
            _dbService = dbService ?? throw new ArgumentNullException(nameof(dbService));
            _repositoryAdapter = repositoryAdapter; // Optional for now
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            _settingsService = settingsService ?? throw new ArgumentNullException(nameof(settingsService));
            _themeService = themeService ?? throw new ArgumentNullException(nameof(themeService));
            _customerService = customerService ?? throw new ArgumentNullException(nameof(customerService));
            _cashDrawerService = cashDrawerService ?? throw new ArgumentNullException(nameof(cashDrawerService));
            _soundService = soundService ?? throw new ArgumentNullException(nameof(soundService));
            _discountService = discountService ?? throw new ArgumentNullException(nameof(discountService));
            _invoiceNumberService = invoiceNumberService ?? throw new ArgumentNullException(nameof(invoiceNumberService));
            _stockService = stockService ?? App.ServiceProvider?.GetService(typeof(IStockService)) as IStockService ?? new POSSystem.Services.InventoryManagement.StockService(_dbService);

            InitializeViewModel();
        }

        // Legacy parameterless constructor for backward compatibility - now uses proper DI
        public SaleViewModel() : this(
            (IDatabaseService)App.ServiceProvider?.GetService(typeof(IDatabaseService)) ??
                throw new InvalidOperationException("DatabaseService not available from DI. Ensure ServiceConfiguration is properly initialized."),
            (IAuthenticationService)App.ServiceProvider?.GetService(typeof(IAuthenticationService)) ??
                new AuthenticationService((DatabaseService)App.ServiceProvider?.GetService(typeof(IDatabaseService)) ??
                    throw new InvalidOperationException("DatabaseService not available for AuthenticationService")),
            (ISettingsService)App.ServiceProvider?.GetService(typeof(ISettingsService)) ?? new SettingsService(),
            (IThemeService)App.ServiceProvider?.GetService(typeof(IThemeService)) ?? new ThemeService(),
            (ICustomerService)App.ServiceProvider?.GetService(typeof(ICustomerService)) ?? new CustomerManagementService(),
            (ICashDrawerService)App.ServiceProvider?.GetService(typeof(ICashDrawerService)) ??
                new CashDrawerService((DatabaseService)App.ServiceProvider?.GetService(typeof(IDatabaseService)) ??
                    throw new InvalidOperationException("DatabaseService not available for CashDrawerService")),
            (ISoundService)App.ServiceProvider?.GetService(typeof(ISoundService)) ?? new SoundService(),
            (IDiscountService)App.ServiceProvider?.GetService(typeof(IDiscountService)) ?? new DiscountService(),
            (IInvoiceNumberService)App.ServiceProvider?.GetService(typeof(IInvoiceNumberService)) ?? new InvoiceNumberService(),
            (RepositoryServiceAdapter)App.ServiceProvider?.GetService(typeof(RepositoryServiceAdapter)))
        {
            System.Diagnostics.Debug.WriteLine("[SALE_VM] Using legacy constructor with proper DI services");
        }

        private void InitializeViewModel()
        {

            // Initialize search timer
            _searchTimer = new System.Timers.Timer(SEARCH_DELAY_MS);
            _searchTimer.AutoReset = false;
            _searchTimer.Elapsed += (s, e) =>
            {
                Dispatcher.CurrentDispatcher.Invoke(async () =>
                {
                    await SearchProducts();
                });
            };

            // Get current sales layout theme
            CurrentSalesLayoutTheme = _settingsService.GetSetting("SalesLayoutTheme") ?? "Grid";

            // Initialize collections
            _allProducts = new ObservableCollection<Product>();
            FilteredProducts = new ObservableCollection<Product>();
            Categories = new ObservableCollection<Category>();
            Carts = new ObservableCollection<Cart>();

            // Set initial state for favorites
            _showingFavorites = true;
            _showingPopularItems = false;

            POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Initial state: ShowingFavorites={_showingFavorites}, ShowingPopularItems={_showingPopularItems}");

            // Load initial data
            LoadInitialData();
            CreateNewCart(); // Create initial cart

            // Subscribe to category updates
            CategoriesViewModel.CategoryChanged += OnCategoryChanged;
        }

        public void Dispose()
        {
            // Unsubscribe from events
            CategoriesViewModel.CategoryChanged -= OnCategoryChanged;

            // Dispose of the search timer
            if (_searchTimer != null)
            {
                _searchTimer.Stop();
                _searchTimer.Elapsed -= (s, e) => { }; // Remove event handler
                _searchTimer.Dispose();
            }

            // Dispose of cancellation token source
            _searchCancellation?.Cancel();
            _searchCancellation?.Dispose();

            // Clear collections
            _allProducts?.Clear();
            FilteredProducts?.Clear();
            Categories?.Clear();
            Carts?.Clear();
        }

        private async void OnCategoryChanged(object sender, CategoryUpdateEventArgs e)
        {
            await RefreshCategories();
        }

        public async Task RefreshCategories()
        {
            try
            {
                using (var context = new POSDbContext())
                {
                    var categories = await context.Categories
                        .AsNoTracking()
                        .OrderBy(c => c.Name)
                        .ToListAsync();

                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        Categories = new ObservableCollection<Category>(categories);
                        // Restore previous selection if possible
                        if (SelectedCategory != null)
                        {
                            var previousCategoryId = SelectedCategory.Id;
                            SelectedCategory = Categories.FirstOrDefault(c => c.Id == previousCategoryId);
                        }
                    });
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing categories: {ex.Message}",
                    "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadInitialData()
        {
            POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] LoadInitialData called");

            // ✅ CRITICAL FIX: Move all heavy operations to background thread to prevent UI blocking
            Task.Run(async () =>
            {
                try
                {
                    await Application.Current.Dispatcher.InvokeAsync(() => IsLoading = true);

                    var context = ((DatabaseService)_dbService).Context;
                    {
                        // Load categories first with minimal data
                        var categories = await context.Categories
                            .AsNoTracking()
                            .Select(c => new Category { Id = c.Id, Name = c.Name })
                            .OrderBy(c => c.Name)
                            .ToListAsync();

                        await Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            Categories = new ObservableCollection<Category>(categories);
                        }, DispatcherPriority.Background);

                    // Load initial set of products (favorites or popular)
                    // Extract user ID to local variable to avoid LINQ parameter evaluation issues
                    var currentUserId = CurrentUser?.Id;
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] LoadInitialData: CurrentUser={CurrentUser?.Username ?? "null"}, UserId={currentUserId}");

                    Task<List<Product>> favoritesTask = null;

                    if (currentUserId.HasValue)
                    {
                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] Loading favorites for logged-in user");
                        // Use DatabaseService method which has proper error handling for schema issues
                        favoritesTask = _dbService.GetUserFavoritesAsync(currentUserId.Value);
                    }

                    // Wait for data and map to products
                    List<Product> products = new List<Product>();

                    if (favoritesTask != null)
                    {
                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] Processing favorites data");
                        var favorites = await favoritesTask;

                        // Set favorite status for all products
                        foreach (var product in favorites)
                        {
                            product.IsFavorited = true;
                        }

                        products = favorites.Take(20).ToList(); // Limit to 20 for initial load

                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Loaded {products.Count} favorite products");
                    }
                    else
                    {
                        Debug.WriteLine("[CART DEBUG] No current user, loading popular products instead");
                        // Load popular products as fallback when no user is logged in
                        var popularProductsData = await context.Products
                            .AsNoTracking()
                            .Where(p => p.IsActive)
                            .OrderByDescending(p => p.Id) // Simple ordering, could be improved with actual popularity metrics
                            .Take(20)
                            .Select(p => new
                            {
                                p.Id,
                                p.Name,
                                p.SKU,
                                p.SellingPrice,
                                p.StockQuantity,
                                p.ReorderPoint,
                                p.MinimumStock,
                                p.IsActive,
                                p.ExpiryDate,
                                p.ImageData,
                                p.TrackBatches,
                                Category = new { p.Category.Id, p.Category.Name },
                                Barcodes = p.Barcodes.Select(b => new { b.Barcode }).ToList(),
                                Batches = p.Batches.Select(b => new { b.Id, b.Quantity }).ToList()
                            })
                            .ToListAsync();

                        // ✅ BATCH OPTIMIZATION: Pre-load batch data to prevent UI blocking
                        products = popularProductsData.Select(p => new Product
                        {
                            Id = p.Id,
                            Name = p.Name,
                            SKU = p.SKU,
                            SellingPrice = p.SellingPrice,
                            StockQuantity = p.StockQuantity,
                            ReorderPoint = p.ReorderPoint,
                            MinimumStock = p.MinimumStock,
                            IsActive = p.IsActive,
                            ExpiryDate = p.ExpiryDate,
                            ImageData = p.ImageData,
                            TrackBatches = p.TrackBatches,
                            Category = new Category { Id = p.Category.Id, Name = p.Category.Name },
                            Barcodes = p.Barcodes.Select(b => new ProductBarcode { Barcode = b.Barcode, ProductId = p.Id }).ToList(),
                            // ✅ CRITICAL FIX: Pre-load batches as HashSet to prevent GetTotalStock() fallback
                            Batches = new HashSet<BatchStock>(p.Batches.Select(b => new BatchStock { Id = b.Id, Quantity = b.Quantity, ProductId = p.Id })),
                            IsFavorited = false
                        }).ToList();

                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Loaded {products.Count} popular products");
                    }

                    // Update UI with batched updates
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        const int batchSize = 5;
                        AllProducts = new ObservableCollection<Product>();
                        FilteredProducts = new ObservableCollection<Product>();

                        for (int i = 0; i < products.Count; i += batchSize)
                        {
                            var batch = products.Skip(i).Take(batchSize);
                            foreach (var product in batch)
                            {
                                AllProducts.Add(product);
                                FilteredProducts.Add(product);
                            }

                            if (i + batchSize < products.Count)
                            {
                                // Allow UI to update between batches
                                System.Windows.Threading.Dispatcher.CurrentDispatcher.Invoke(
                                    () => { }, System.Windows.Threading.DispatcherPriority.Background);
                            }
                        }

                        ShowingFavorites = currentUserId.HasValue;
                        ShowingPopularItems = !currentUserId.HasValue;

                        Debug.WriteLine($"[CART DEBUG] LoadInitialData completed. Products loaded: {FilteredProducts.Count}, ShowingFavorites: {ShowingFavorites}, ShowingPopularItems: {ShowingPopularItems}");
                    }, DispatcherPriority.Background);
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error loading initial data: {ex.Message}");

                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        // If it's a database schema issue, don't show error to user, just initialize empty
                        if (ex.Message.Contains("no such column") || ex.Message.Contains("SQLite Error"))
                        {
                            Debug.WriteLine("[CART DEBUG] Database schema issue detected in LoadInitialData, initializing empty collections");
                        }
                        else
                        {
                            MessageBox.Show($"Error loading initial data: {ex.Message}",
                                "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                        }

                        // Initialize with empty collections
                        AllProducts = new ObservableCollection<Product>();
                        FilteredProducts = new ObservableCollection<Product>();
                        Categories = new ObservableCollection<Category>();
                    }, DispatcherPriority.Background);
                }
                finally
                {
                    await Application.Current.Dispatcher.InvokeAsync(() => IsLoading = false);
                }
            }); // End of Task.Run
        }

        public async Task<ObservableCollection<Product>> LoadPopularProducts()
        {
            using var __perfLoadPopular = new POSSystem.Helpers.PerfTimer("Products.LoadPopular");
                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] LoadPopularProducts called");
                try
            {
                var context = ((DatabaseService)_dbService).Context;
                {
                    // Configure context for better performance
                    context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                    context.ChangeTracker.AutoDetectChangesEnabled = false;

                    // ✅ ULTRA-FAST FIX: Use emergency timeout and simplified query for maximum speed
                    var popularProductsData = await EmergencyPerformanceFix.ExecuteWithEmergencyTimeout(async () =>
                    {
                        return await DatabasePerformanceHelper.ExecuteQueryAsync(async context =>
                        {
                            // ✅ STOCK CONSISTENCY FIX: Include batch data for accurate stock calculation
                            return await context.Products
                                .AsNoTracking()
                                .Include(p => p.PriceTiers.Where(pt => pt.IsActive))
                                .Include(p => p.Category)
                                .Include(p => p.Barcodes)
                                // IMPORTANT: Do not include Batches; rely on StockQuantity as source of truth
                                .Where(p => p.IsActive && p.Id > 0) // ✅ FILTER FIX: Exclude custom items (negative IDs) from Popular category
                                .OrderByDescending(p => p.Id) // Simple popularity ordering - could be enhanced with actual sales data
                                .Take(POPULAR_ITEMS_COUNT)
                                .ToListAsync();
                        }, "LoadPopularProductsData_UltraFast");
                    }, 3000, "LoadPopularProductsData_UltraFast"); // 3 second emergency timeout

                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Found {popularProductsData?.Count ?? 0} popular products from database");

                    // Stock quantity is already up-to-date from the sale commit; no batch override here

                    // ✅ BULK PRICING FIX: Products now include pricing tiers automatically
                    var popularProducts = popularProductsData.ToList();

                    // Log bulk pricing information for debugging
                    foreach (var product in popularProducts)
                    {
                        System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCTS] Product {product.Name} (ID: {product.Id}) has {product.PriceTiers?.Count ?? 0} pricing tiers, HasBulkPricing: {product.HasBulkPricing}");
                    }

                    // ✅ PERFORMANCE FIX: Update UI with emergency protection and efficient collection replacement
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        PerformanceHelper.BatchUpdate(() => {
                            CollectionDiffer.ApplyByKey(FilteredProducts, popularProducts, p => p.Id);
                            CollectionDiffer.ApplyByKey(AllProducts, popularProducts, p => p.Id);
                        });

                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Updated UI with {FilteredProducts.Count} popular products");
                    }, DispatcherPriority.Normal);

                    // Force UI yield to prevent blocking
                    await EmergencyPerformanceFix.ForceUIYield();

                    return new ObservableCollection<Product>(popularProducts);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading popular products: {ex.Message}");
                return new ObservableCollection<Product>();
            }
        }

        public ObservableCollection<Product> LoadRecentSaleProducts()
        {
            var recentSales = _dbService.GetRecentSales(10);
            var recentProducts = recentSales
                .SelectMany(s => s.Items.Select(i => i.Product))
                .Distinct()
                .ToList();
            return new ObservableCollection<Product>(recentProducts);
        }

        public async void FilterProductsByCategory(int categoryId)
        {
            try
            {
                using var __perfCatChange = new POSSystem.Helpers.PerfTimer("CategoryChange.FilterProductsByCategory");
                    IsLoading = true;
                    _currentPage = 0;
                    _hasMoreItems = true;
                    ShowingPopularItems = false;
                    ShowingFavorites = false;

                // Check if this is "All Categories" selection
                bool isAllCategories = categoryId == 0 || categoryId == -1;

                // Update the selected category based on the categoryId
                if (isAllCategories)
                {
                    SelectedCategory = null;
                    // For All Categories, we'll use a different approach to show popular products initially
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        FilteredProducts.Clear();
                        // Keep AllProducts intact to avoid full reset; next load will diff in
                    });
                    await LoadPopularProducts();
                    _hasMoreItems = true; // Ensure we can load more beyond popular items
                    return;
                }
                else
                {
                    // Find and set the selected category
                    SelectedCategory = Categories.FirstOrDefault(c => c.Id == categoryId);
                }

                // Use optimized initial load size for better performance
                int initialLoadSize = INITIAL_LOAD_SIZE;

                var context = ((DatabaseService)_dbService).Context;
                {
                    // Configure context for better performance
                    context.ChangeTracker.QueryTrackingBehavior = QueryTrackingBehavior.NoTracking;
                    context.ChangeTracker.AutoDetectChangesEnabled = false;

                    // ✅ STOCK CONSISTENCY FIX: Include batch data for accurate stock calculation
                    var productsData = await context.Products
                        .AsNoTracking()
                        .Include(p => p.Category) // Essential for display
                        .Include(p => p.PriceTiers.Where(pt => pt.IsActive)) // Load pricing for bulk pricing support
                        // IMPORTANT: Do not include Batches; rely on StockQuantity as source of truth
                        .Where(p => p.CategoryId == categoryId && p.IsActive && p.Id > 0) // ✅ CUSTOM PRODUCT FIX: Exclude custom products (negative IDs) from category filtering
                        .OrderBy(p => p.Name) // Add ordering for consistent results
                        .Take(initialLoadSize)
                        .ToListAsync();

                    // Stock quantity is already up-to-date from the sale commit; no batch override here

                    // Calculate if there are more items
                    _hasMoreItems = productsData.Count == initialLoadSize;

                    // Get favorite statuses for these products if user is logged in
                    var userFavorites = new HashSet<int>();
                    var currentUserId = CurrentUser?.Id;
                    if (currentUserId.HasValue)
                    {
                        // Get favorite product IDs for current user
                        var favorites = await context.UserFavorites
                            .AsNoTracking()
                            .Where(f => f.UserId == currentUserId.Value)
                            .Select(f => f.ProductId)
                            .ToListAsync();

                        userFavorites = new HashSet<int>(favorites);
                    }

                    // ✅ BULK PRICING FIX: Products already include pricing tiers, just set favorite status
                    var products = productsData.ToList();

                    // Set favorite status for each product
                    foreach (var product in products)
                    {
                        product.IsFavorited = userFavorites.Contains(product.Id);
                        System.Diagnostics.Debug.WriteLine($"[LOAD_PRODUCTS] Product {product.Name} (ID: {product.Id}) has {product.PriceTiers?.Count ?? 0} pricing tiers, HasBulkPricing: {product.HasBulkPricing}");
                    }

                    // Update the UI on the UI thread in a batch operation
                    // ✅ CRITICAL FIX: Use efficient collection replacement for better performance
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        PerformanceHelper.BatchUpdate(() => {
                            // ✅ FILTER FIX: Always clear and update collections to handle empty categories properly
                            FilteredProducts.Clear();
                            AllProducts.Clear();

                            if (products.Count > 0)
                            {
                                CollectionDiffer.ApplyByKey(FilteredProducts, products, p => p.Id);
                                CollectionDiffer.ApplyByKey(AllProducts, products, p => p.Id);
                            }
                            // If no products, collections remain empty, showing proper empty state
                        });

                        System.Diagnostics.Debug.WriteLine($"[SALESVIEWGRID] Category filter applied - {products.Count} products loaded for category {categoryId}");
                    }, System.Windows.Threading.DispatcherPriority.Background);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error filtering products by category: {ex.Message}");
                MessageBox.Show($"Error loading products: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        public void CloseCart(Cart cart)
        {
            if (cart == null || !Carts.Contains(cart)) return;

            // If the cart has items, ask for confirmation (regardless of whether it's active)
            if (cart.Items.Any())
            {
                var result = MessageBox.Show(
                    Application.Current.TryFindResource("ConfirmCloseCart") as string ?? "Are you sure you want to close this cart? All items will be lost.",
                    Application.Current.TryFindResource("Confirm") as string ?? "Close Cart",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.No)
                {
                    return;
                }
            }

            // Check if this is a sale edit cart (contains invoice number in name)
            bool isSaleCart = cart.Name != null && cart.Name.StartsWith("Invoice #");

            // If it's a sale cart and is the active one, handle differently
            if (isSaleCart && cart == ActiveCart && IsEditMode)
            {
                // Find a "Cart 1" that might already exist
                var regularCart = Carts.FirstOrDefault(c => c.Name != null && c.Name.StartsWith("Cart 1"));

                // Remove the invoice cart
                Carts.Remove(cart);

                // Clear sale state
                IsEditMode = false;
                CurrentLoadedSale = null;
                _currentSaleId = null;
                CurrentInvoiceNumber = null;

                // Clear the customer that was associated with the sale
                SelectedCustomer = null;

                // If we found a regular cart, use it rather than creating a new one
                if (regularCart != null && Carts.Contains(regularCart))
                {
                    ActiveCart = regularCart;
                    CurrentCart = regularCart;
                }
                else if (Carts.Count == 0)
                {
                    // Only create a new cart if there are none left
                    CreateNewCart();
                }
                else
                {
                    // Use the first available cart
                    ActiveCart = Carts[0];
                    CurrentCart = Carts[0];
                }

                return;
            }

            Carts.Remove(cart);

            // Always ensure we have at least one cart
            if (Carts.Count == 0)
            {
                CreateNewCart();
            }
            else if (cart == ActiveCart)
            {
                // Switch to the first available cart
                ActiveCart = Carts[0];
                CurrentCart = Carts[0];
            }

            OnPropertyChanged(nameof(CanCloseCart));
        }

        public void PrintReceipt()
        {
            // Implement receipt printing logic
            // You might want to use a separate service for this
        }

        private void CartItems_CollectionChanged(object sender, NotifyCollectionChangedEventArgs e)
        {
            OnPropertyChanged(nameof(CartItems));
            OnPropertyChanged(nameof(TotalItems));
            OnPropertyChanged(nameof(CurrentCart));
            OnPropertyChanged(nameof(HasCartItems));
            OnPropertyChanged(nameof(CanCloseCart));
            CalculateTotals();
        }

        public void CalculateTotals()
        {
            if (ActiveCart == null || ActiveCart.Items == null)
            {
                Subtotal = 0;
                DiscountAmount = 0;
                TaxAmount = 0;
                GrandTotal = 0;
                return;
            }

            // Calculate totals in a single pass
            var (newSubtotal, itemDiscounts) = ActiveCart.Items.Aggregate(
                (Subtotal: 0m, Discounts: 0m),
                (acc, item) => (
                    acc.Subtotal + item.Total,
                    acc.Discounts + (item.Discounts?.Sum(d => d.DiscountValue) ?? 0m)
                )
            );

            // Add cart-wide discounts
            var cartDiscounts = ActiveCart.Discounts?.Sum(d => d.DiscountValue) ?? 0m;
            var totalDiscounts = itemDiscounts + cartDiscounts;

            // ✅ PERFORMANCE FIX: Batch property updates to minimize UI notifications
            bool hasChanges = false;
            var propertiesToUpdate = new List<string>();

            if (Math.Abs(Subtotal - newSubtotal) > 0.01m)
            {
                Subtotal = newSubtotal;
                hasChanges = true;
                propertiesToUpdate.Add(nameof(Subtotal));
            }

            if (Math.Abs(DiscountAmount - totalDiscounts) > 0.01m)
            {
                DiscountAmount = totalDiscounts;
                hasChanges = true;
                propertiesToUpdate.Add(nameof(DiscountAmount));
            }

            if (Math.Abs(TaxAmount - 0) > 0.01m) // We're not using tax in this system
            {
                TaxAmount = 0;
                hasChanges = true;
                propertiesToUpdate.Add(nameof(TaxAmount));
            }

            var newGrandTotal = newSubtotal - totalDiscounts;
            if (Math.Abs(GrandTotal - newGrandTotal) > 0.01m)
            {
                GrandTotal = newGrandTotal;
                hasChanges = true;
                propertiesToUpdate.Add(nameof(GrandTotal));
            }

            // ✅ PERFORMANCE FIX: Batch notify only changed properties
            if (hasChanges)
            {
                // Use background priority to prevent UI blocking
                Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Only notify properties that actually changed
                    foreach (var property in propertiesToUpdate)
                    {
                        OnPropertyChanged(property);
                    }

                    // Always update these related properties
                    OnPropertyChanged(nameof(TotalItems));
                    OnPropertyChanged(nameof(CurrentCart));
                    OnPropertyChanged(nameof(HasCartItems));
                    UpdateLoyaltyInfo();
                }, DispatcherPriority.Background);
            }
        }

        public bool AddToCart(Product product, decimal quantity = 1)
        {
            try
            {
                // Validate input parameters
                if (product == null)
                {
                    Debug.WriteLine("[CART DEBUG] ERROR: Product is null");
                    MessageBox.Show("Cannot add item: Product information is missing.", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                if (quantity <= 0)
                {
                    Debug.WriteLine($"[CART DEBUG] ERROR: Invalid quantity: {quantity}");
                    MessageBox.Show("Cannot add item: Quantity must be greater than 0.", "Error",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                // For unit-based products, ensure quantity is a whole number
                if (!product.IsWeightBased && quantity != Math.Floor(quantity))
                {
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] WARNING: Unit-based product with decimal quantity {quantity}, rounding to {Math.Floor(quantity)}");
                    quantity = Math.Max(1, Math.Floor(quantity));
                }

                // ✅ PERFORMANCE FIX: Conditional debug logging to prevent frame rate drops
                #if DEBUG
                if (POSSystem.Helpers.PerformanceDebugHelper.IsCartDebugEnabled)
                {
                    Debug.WriteLine($"[CART DEBUG] AddToCart called for product: {product.Name} (ID: {product.Id}), quantity: {quantity}");
                }
                #endif

                if (CurrentCart == null)
                {
                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] CurrentCart is null, creating new cart");
                    CreateNewCart();

                    if (CurrentCart == null)
                    {
                        POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug("[CART DEBUG] ERROR: Failed to create new cart");
                        MessageBox.Show("Cannot add item: Failed to initialize cart.", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }

            // ✅ PERFORMANCE FIX: Conditional debug logging to prevent frame rate drops
            #if DEBUG
            if (POSSystem.Helpers.PerformanceDebugHelper.IsCartDebugEnabled)
            {
                Debug.WriteLine($"[CART DEBUG] CurrentCart has {CurrentCart?.Items?.Count ?? 0} items before adding");
            }
            #endif

            // Store the current selection before any operations
            CartItem originalSelectedItem = SelectedCartItem;

            // Skip stock check for custom products (identified by ID = -1) and services
            if (product.Id != -1 && product.Type != ProductType.Service)  // Regular product (not custom, not service)
            {
                // Check if we're adding to an existing item and calculate total quantity
                var currentExistingItem = CurrentCart.Items.FirstOrDefault(item => item.Product.Id == product.Id);
                decimal totalQuantity = (currentExistingItem?.Quantity ?? 0) + quantity;

                // For weight-based products, we need to handle decimal stock quantities
                // ✅ FIX: Get accurate stock quantity, especially for batch-tracked products
                decimal availableStock;
                if (product.TrackBatches)
                {
                    // For batch-tracked products, get the most accurate stock from batches
                    var batchStock = product.GetTotalStockDecimal();

                    // ✅ FIX: If batch calculation returns 0 but StockQuantity is positive, use StockQuantity
                    // This handles cases where batches aren't loaded properly but the product has stock
                    if (batchStock == 0 && product.StockQuantity > 0)
                    {
                        availableStock = product.StockQuantity;
                        System.Diagnostics.Debug.WriteLine($"[STOCK_VALIDATION] Batch-tracked product {product.Name}: Batches not loaded properly, using StockQuantity = {availableStock}");
                    }
                    else
                    {
                        availableStock = batchStock;
                        System.Diagnostics.Debug.WriteLine($"[STOCK_VALIDATION] Batch-tracked product {product.Name}: Using batch total stock = {availableStock}");
                    }
                }
                else
                {
                    // For regular products, use StockQuantity
                    availableStock = product.StockQuantity;
                    System.Diagnostics.Debug.WriteLine($"[STOCK_VALIDATION] Regular product {product.Name}: Using StockQuantity = {availableStock}");
                }

                if (totalQuantity > availableStock)
                {
                    string quantityUnit = product.IsWeightBased ?
                        (product.UnitOfMeasure?.Abbreviation ?? "units") : "items";

                    // Check if stock is completely 0 and user has invoice permissions
                    if (availableStock == 0)
                    {
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Product {product.Name} (ID: {product.Id}) is out of stock");

                            // ✅ FIX: Use DI container instead of ServiceLocator
                            var permissionsService = App.ServiceProvider?.GetService(typeof(UserPermissionsService)) as UserPermissionsService;
                            System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] PermissionsService obtained: {permissionsService != null}");
                            System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] PermissionsService created: {permissionsService != null}");

                            bool canCreateInvoices = false;
                            try
                            {
                                canCreateInvoices = permissionsService?.CanCreateDraftInvoices() == true || permissionsService?.CanCreateFullInvoices() == true;
                                System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Permission check completed: {canCreateInvoices}");
                            }
                            catch (Exception permEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Error checking permissions: {permEx.Message}");
                            }

                        // TEMPORARY: For testing purposes, allow invoice creation even without login
                        // TODO: Remove this after testing and ensure proper user authentication
                        if (!canCreateInvoices)
                        {
                            System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] No user logged in, enabling test mode for invoice creation");
                            canCreateInvoices = true; // Enable for testing
                        }

                        System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Can create invoices: {canCreateInvoices}");

                        if (canCreateInvoices)
                        {
                            try
                            {
                                System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Showing invoice creation prompt");

                                var title = Application.Current.FindResource("OutOfStockCreateInvoiceTitle") as string ?? "Out of Stock - Create Invoice?";
                                var message = Application.Current.FindResource("OutOfStockCreateInvoiceMessage") as string ?? $"This product is out of stock (0 {quantityUnit} available).\n\nWould you like to create an invoice for this product instead?";

                                System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Dialog title: '{title}'");
                                System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Dialog message: '{message}'");

                                var result = POSSystem.Helpers.LocalizedMessageBox.Show(
                                    message,
                                    title,
                                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNo,
                                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                                System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Dialog result: {result}");

                                if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.Yes)
                                {
                                    System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] User chose to create invoice");
                                    // Open invoice creation dialog
                                    _ = Task.Run(async () =>
                                    {
                                        try
                                        {
                                            await Application.Current.Dispatcher.InvokeAsync(async () =>
                                            {
                                                await CreateInvoiceFromOutOfStockProduct(product);
                                            });
                                        }
                                        catch (Exception ex)
                                        {
                                            System.Diagnostics.Debug.WriteLine($"[INVOICE] Error creating invoice from out-of-stock product: {ex.Message}");
                                        }
                                    });
                                    return false; // Don't add to cart
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] User chose not to create invoice");
                                }
                            }
                            catch (Exception dialogEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Error showing dialog: {dialogEx.Message}");
                                var errorTitle = Application.Current.FindResource("DialogErrorTitle") as string ?? "Error";
                                _ = Task.Run(async () => await POSSystem.Helpers.LocalizedMessageBox.ShowErrorAsync($"Error showing dialog: {dialogEx.Message}", errorTitle));
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] User cannot create invoices, showing simple message");

                            var title = Application.Current.FindResource("OutOfStockTitle") as string ?? "Out of Stock";
                            var messageFormat = Application.Current.FindResource("OutOfStockWithQuantityMessage") as string ?? "This product is out of stock (0 {0} available).";
                            var message = string.Format(messageFormat, quantityUnit);

                            _ = Task.Run(async () => await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(message, title,
                                POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.OK,
                                POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Warning));
                        }
                        }
                        catch (Exception outOfStockEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Critical error in out-of-stock handling: {outOfStockEx.Message}");
                            System.Diagnostics.Debug.WriteLine($"[OUT_OF_STOCK] Stack trace: {outOfStockEx.StackTrace}");
                            var errorTitle = Application.Current.FindResource("DialogErrorTitle") as string ?? "Error";
                            _ = Task.Run(async () => await POSSystem.Helpers.LocalizedMessageBox.ShowErrorAsync($"Error handling out-of-stock product: {outOfStockEx.Message}", errorTitle));
                        }
                    }
                    else
                    {
                        // Enhanced insufficient stock handling with reserve invoice option
                        try
                        {
                            System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Product {product.Name} (ID: {product.Id}) has insufficient stock. Available: {availableStock}, Requested: {quantity}");

                            // ✅ FIX: Use DI container instead of ServiceLocator
                            var permissionsService = App.ServiceProvider?.GetService(typeof(UserPermissionsService)) as UserPermissionsService;
                            System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] PermissionsService obtained: {permissionsService != null}");

                            bool canCreateInvoices = false;
                            try
                            {
                                canCreateInvoices = permissionsService?.CanCreateDraftInvoices() == true || permissionsService?.CanCreateFullInvoices() == true;
                                System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Permission check completed: {canCreateInvoices}");
                            }
                            catch (Exception permEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Error checking permissions: {permEx.Message}");
                            }

                            // TEMPORARY: For testing purposes, allow invoice creation even without login
                            // TODO: Remove this after testing and ensure proper user authentication
                            if (!canCreateInvoices)
                            {
                                System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] No user logged in, enabling test mode for invoice creation");
                                canCreateInvoices = true; // Enable for testing
                            }

                            System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Can create invoices: {canCreateInvoices}");

                            if (canCreateInvoices)
                            {
                                try
                                {
                                    System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Showing enhanced insufficient stock dialog with invoice option");

                                    var title = Application.Current.FindResource("InsufficientStockTitle") as string ?? "Insufficient Stock";
                                    var messageFormat = Application.Current.FindResource("InsufficientStockWithInvoiceMessage") as string ??
                                        "Only {0} {1} available in stock.\n\nAdd available stock to cart?";

                                    // Format quantity appropriately for unit-based vs weight-based products
                                    string formattedQuantity = product.IsWeightBased ?
                                        $"{availableStock:F3}" : $"{availableStock:F0}";
                                    var message = string.Format(messageFormat, formattedQuantity, quantityUnit);

                                    System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Dialog title: '{title}'");
                                    System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Dialog message: '{message}'");

                                    // Use Yes/No/Cancel dialog for better UX
                                    var result = POSSystem.Helpers.LocalizedMessageBox.Show(
                                        message,
                                        title,
                                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.YesNoCancel,
                                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Question);

                                    System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Dialog result: {result}");

                                    if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.Yes)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] User chose to add available stock");
                                        // Continue to the partial quantity logic below
                                    }
                                    else if (result == POSSystem.Helpers.LocalizedMessageBox.MessageBoxResult.No)
                                    {
                                        System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] User chose to create reserve invoice");
                                        // Open invoice creation dialog
                                        _ = Task.Run(async () =>
                                        {
                                            try
                                            {
                                                await Application.Current.Dispatcher.InvokeAsync(async () =>
                                                {
                                                    await CreateInvoiceFromOutOfStockProduct(product);
                                                });
                                            }
                                            catch (Exception ex)
                                            {
                                                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error creating invoice from insufficient stock product: {ex.Message}");
                                            }
                                        });
                                        return false; // Don't add to cart
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] User chose to cancel");
                                        return false; // Don't add to cart
                                    }
                                }
                                catch (Exception dialogEx)
                                {
                                    System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Error showing enhanced dialog: {dialogEx.Message}");
                                    // Fall back to simple dialog
                                    var title = Application.Current.FindResource("StockLimitTitle") as string ?? "Stock Limit";
                                    var messageFormat = Application.Current.FindResource("StockLimitMessage") as string ?? "Cannot add that quantity. Only {0} {1} available in stock.";

                                    // Format quantity appropriately for unit-based vs weight-based products
                                    string formattedQuantity = product.IsWeightBased ?
                                        $"{availableStock:F3}" : $"{availableStock:F0}";
                                    var message = string.Format(messageFormat, formattedQuantity, quantityUnit);

                                    _ = Task.Run(async () => await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(message, title,
                                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.OK,
                                        POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Warning));
                                }
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] User cannot create invoices, showing simple message");

                                var title = Application.Current.FindResource("StockLimitTitle") as string ?? "Stock Limit";
                                var messageFormat = Application.Current.FindResource("StockLimitMessage") as string ?? "Cannot add that quantity. Only {0} {1} available in stock.";

                                // Format quantity appropriately for unit-based vs weight-based products
                                string formattedQuantity = product.IsWeightBased ?
                                    $"{availableStock:F3}" : $"{availableStock:F0}";
                                var message = string.Format(messageFormat, formattedQuantity, quantityUnit);

                                _ = Task.Run(async () => await POSSystem.Helpers.LocalizedMessageBox.ShowAsync(message, title,
                                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxButton.OK,
                                    POSSystem.Helpers.LocalizedMessageBox.MessageBoxImage.Warning));
                            }
                        }
                        catch (Exception insufficientStockEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Critical error in insufficient stock handling: {insufficientStockEx.Message}");
                            System.Diagnostics.Debug.WriteLine($"[INSUFFICIENT_STOCK] Stack trace: {insufficientStockEx.StackTrace}");
                            var errorTitle = Application.Current.FindResource("DialogErrorTitle") as string ?? "Error";
                            _ = Task.Run(async () => await POSSystem.Helpers.LocalizedMessageBox.ShowErrorAsync($"Error handling insufficient stock product: {insufficientStockEx.Message}", errorTitle));
                        }
                    }

                    // Check if we can add a partial quantity
                    if (availableStock > 0 && (currentExistingItem == null || currentExistingItem.Quantity < availableStock))
                    {
                        decimal availableQuantity = availableStock - (currentExistingItem?.Quantity ?? 0);
                        if (availableQuantity <= 0)
                        {
                            // Preserve original selection with ApplicationIdle priority
                            if (originalSelectedItem != null)
                            {
                                Application.Current.Dispatcher.InvokeAsync(() => SelectedCartItem = originalSelectedItem,
                                    DispatcherPriority.ApplicationIdle);
                            }
                            return false;
                        }

                        string unitName = product.IsWeightBased ?
                            (product.UnitOfMeasure?.Abbreviation ?? "units") : "items";
                        string quantityDisplay = product.IsWeightBased ?
                            $"{availableQuantity:F3}" : $"{availableQuantity:F0}";

                        var result = MessageBox.Show(
                            $"Would you like to add {quantityDisplay} {unitName} instead (the maximum available)?",
                            "Add Available Stock",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            // Update the quantity to the available amount
                            quantity = availableQuantity;
                        }
                        else
                        {
                            // Preserve original selection with ApplicationIdle priority if user cancels
                            if (originalSelectedItem != null)
                            {
                                Application.Current.Dispatcher.InvokeAsync(() => SelectedCartItem = originalSelectedItem,
                                    DispatcherPriority.ApplicationIdle);
                            }
                            return false;
                        }
                    }
                    else
                    {
                        // Preserve original selection with ApplicationIdle priority
                        if (originalSelectedItem != null)
                        {
                            Application.Current.Dispatcher.InvokeAsync(() => SelectedCartItem = originalSelectedItem,
                                DispatcherPriority.ApplicationIdle);
                        }
                        return false;
                    }
                }
            }

            // First update in UI thread
            CartItem targetItem = null;
            var existingItem = CurrentCart.Items.FirstOrDefault(item => item.Product.Id == product.Id);
            if (existingItem != null)
            {
                // Determine current FIFO batch selling price to decide whether to split into a new line
                decimal currentBatchPrice = product.TrackBatches
                    ? (_dbService.GetBatchesForProduct(product.Id)
                        .Where(b => b.Quantity > 0)
                        .OrderBy(b => b.CreatedAt)
                        .ThenBy(b => b.Id)
                        .FirstOrDefault()?.SellingPrice ?? product.SellingPrice)
                    : product.SellingPrice;

                // Update UI on the main thread immediately
                Application.Current.Dispatcher.Invoke(() =>
                {
                    if (Math.Abs(existingItem.UnitPrice - currentBatchPrice) < 0.0001m)
                    {
                        // Same price: append quantity to existing line
                        existingItem.Quantity += quantity;
                        targetItem = existingItem;
                    }
                    else
                    {
                        // Different price (likely moved to next batch): create a new line with the new batch price
                        var newItemForNextBatch = new CartItem
                        {
                            Product = product,
                            Quantity = quantity,
                            UnitPrice = currentBatchPrice,
                            BatchUnitPrice = currentBatchPrice
                        };
                        CurrentCart.Items.Add(newItemForNextBatch);
                        targetItem = newItemForNextBatch;
                    }

                    // Set the selected cart item to show in preview card
                    SelectedCartItem = targetItem;

                    // Play the add to cart sound
                    _soundService.PlayCartAddSound();

                    CalculateTotals();
                });
            }
            else
            {
                // Create new item and add to cart on the main thread
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var newItem = new CartItem
                    {
                        Product = product,
                        Quantity = quantity,
                        UnitPrice = product.TrackBatches
                            ? (_dbService.GetBatchesForProduct(product.Id)
                                .Where(b => b.Quantity > 0)
                                .OrderBy(b => b.CreatedAt)
                                .ThenBy(b => b.Id)
                                .FirstOrDefault()?.SellingPrice ?? product.SellingPrice)
                            : product.SellingPrice,
                        BatchUnitPrice = product.TrackBatches
                            ? (_dbService.GetBatchesForProduct(product.Id)
                                .Where(b => b.Quantity > 0)
                                .OrderBy(b => b.CreatedAt)
                                .ThenBy(b => b.Id)
                                .FirstOrDefault()?.SellingPrice)
                            : null
                    };
                    CurrentCart.Items.Add(newItem);
                    targetItem = newItem;

                    POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Added new item to cart. Cart now has {CurrentCart.Items.Count} items");

                    // Set the selected cart item to show in preview card
                    SelectedCartItem = targetItem;

                    // Play the add to cart sound
                    _soundService.PlayCartAddSound();

                    CalculateTotals();
                });
            }

            // ✅ PERFORMANCE FIX: Conditional debug logging to prevent frame rate drops
            #if DEBUG
            if (POSSystem.Helpers.PerformanceDebugHelper.IsCartDebugEnabled)
            {
                Debug.WriteLine($"[CART DEBUG] CurrentCart has {CurrentCart?.Items?.Count ?? 0} items after adding");
                Debug.WriteLine($"[CART DEBUG] ActiveCart has {ActiveCart?.Items?.Count ?? 0} items after adding");
            }
            #endif

            // Make sure selection is preserved even after other UI updates
            if (targetItem != null)
            {
                var item = targetItem;
                Application.Current.Dispatcher.InvokeAsync(() => SelectedCartItem = item, DispatcherPriority.ApplicationIdle);
            }

            return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[CART DEBUG] ERROR in AddToCart: {ex.Message}");
                Debug.WriteLine($"[CART DEBUG] Stack trace: {ex.StackTrace}");

                MessageBox.Show($"Failed to add item to cart: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                return false;
            }
        }

        /// <summary>
        /// Shows the weight-based product dialog for quantity/amount selection
        /// </summary>
        private void ShowWeightBasedProductDialog(Product product)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Opening dialog for weight-based product: {product.Name}");

                Application.Current.Dispatcher.Invoke(() =>
                {
                    var dialog = new Views.Dialogs.WeightBasedProductDialog(product)
                    {
                        Owner = Application.Current.MainWindow
                    };

                    bool? result = dialog.ShowDialog();

                    if (result == true && dialog.IsConfirmed)
                    {
                        // Add the product to cart with the selected weight
                        decimal selectedWeight = dialog.SelectedWeight;
                        decimal selectedAmount = dialog.SelectedAmount;

                        System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Adding to cart: {product.Name}, Weight: {selectedWeight:F2}, Amount: ${selectedAmount:F2}");

                        // Add to cart with the selected weight as quantity
                        if (product.TrackBatches)
                        {
                            AllocateIntoCartByBatches(product, selectedWeight);
                        }
                        else
                        {
                            AddToCart(product, selectedWeight);
                        }

                        // Show confirmation message
                        var confirmationMessage = $"Added {selectedWeight:F2} units of {product.Name} (${selectedAmount:F2}) to cart";
                        System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] {confirmationMessage}");
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Dialog cancelled for product: {product.Name}");
                    }
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[WEIGHT-DIALOG] Error showing dialog: {ex.Message}");
                MessageBox.Show($"Error opening weight selection dialog: {ex.Message}", "Error",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public void RemoveFromCart(int productId)
        {
            var item = CartItems?.FirstOrDefault(i => i.Product.Id == productId);
            if (item != null)
            {
                // Use a single UI update
                Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    ActiveCart.Items.Remove(item);
                    CalculateTotals();
                }, DispatcherPriority.Background);
            }
        }

        public void CreateNewCart()
        {
            POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Creating new cart. Current cart count: {Carts?.Count ?? 0}");

            var newCart = new Cart
            {
                Name = $"Cart {Carts.Count + 1}",
                CreatedAt = DateTime.Now
            };
            Carts.Add(newCart);
            ActiveCart = newCart;
            CurrentCart = newCart;

            POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Created new cart '{newCart.Name}'. Total carts: {Carts.Count}");
            POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] ActiveCart == CurrentCart: {ActiveCart == CurrentCart}");

            OnPropertyChanged(nameof(CanCloseCart));
        }

        public void SwitchToCart(Cart cart)
        {
            if (cart != null && Carts.Contains(cart))
            {
                ActiveCart = cart;
                CurrentCart = cart;
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged([CallerMemberName] string name = null)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(name));

        internal void RemoveFromCart(object id)
        {
            RemoveFromCart(Convert.ToInt32(id));
        }

        public async Task<bool> ProcessPayment(string paymentMethod, decimal amountTendered, DateTime? dueDate = null)
        {
            try
            {
                var currentUser = _dbService.GetDefaultUser();
                if (currentUser == null)
                {
                    MessageBox.Show("No user logged in", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // Check if cash drawer is open when payment method is Cash
                CashDrawer currentDrawer = null;
                if (paymentMethod == "Cash")
                {
                    try
                    {
                        currentDrawer = _cashDrawerService.GetCurrentDrawerBasic();
                        if (currentDrawer == null || currentDrawer.Status != "Open")
                        {
                            // Show the cash drawer dialog instead of throwing an error
                            var dialog = new Views.OpenDrawerDialog();

                            // Try different dialog identifiers to ensure it works from payment context
                            object result = null;
                            try
                            {
                                result = await DialogHost.Show(dialog, "RootDialog");
                            }
                            catch
                            {
                                try
                                {
                                    result = await DialogHost.Show(dialog, "MainWindowCashDrawerDialog");
                                }
                                catch
                                {
                                    result = await DialogHost.Show(dialog, "SalesDialog");
                                }
                            }

                            if (result is decimal openingBalance)
                            {
                                var user = currentUser;
                                var drawer = new CashDrawer
                                {
                                    OpeningBalance = openingBalance,
                                    OpenedById = user.Id,
                                    Status = "Open",
                                    Transactions = new ObservableCollection<CashTransaction>()
                                };

                                try
                                {
                                    _cashDrawerService.OpenDrawer(drawer);

                                    // Add a small delay to ensure the drawer is fully opened in the database
                                    await Task.Delay(200);

                                    // Get the newly opened drawer
                                    currentDrawer = _cashDrawerService.GetCurrentDrawerBasic();
                                    if (currentDrawer == null)
                                    {
                                        MessageBox.Show("Could not find the opened cash drawer.", "Error",
                                            MessageBoxButton.OK, MessageBoxImage.Error);
                                        return false;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    MessageBox.Show($"Error opening cash drawer: {ex.Message}", "Error",
                                        MessageBoxButton.OK, MessageBoxImage.Error);
                                    return false;
                                }
                            }
                            else
                            {
                                // User cancelled the cash drawer opening
                                return false;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Error checking cash drawer status: {ex.Message}", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }

                // Start a database transaction to ensure everything is consistent
                using (var dbContext = new POSDbContext())
                using (var transaction = dbContext.Database.BeginTransaction())
                {
                    try
                    {
                        // Create the sale object
                        var sale = new Sale
                        {
                            SaleDate = DateTime.Now,
                            CustomerId = SelectedCustomer?.Id == -1 ? null : SelectedCustomer?.Id,
                            UserId = currentUser.Id,
                            Status = "Completed",
                            PaymentStatus = paymentMethod == "Unpaid" ? "Unpaid" : "Paid",
                            PaymentMethod = paymentMethod,
                            // Use the new InvoiceNumberService to avoid INSTRO error
                            InvoiceNumber = _invoiceNumberService.GenerateInvoiceNumber(DateTime.Now),
                            Subtotal = Subtotal,
                            DiscountAmount = DiscountAmount,
                            TaxAmount = TaxAmount,
                            GrandTotal = GrandTotal,
                            AmountPaid = amountTendered,
                            Change = amountTendered - GrandTotal,
                            DueDate = dueDate
                        };

                        // Track custom products that were added successfully
                        List<int> addedCustomProductIds = new List<int>();
                        bool customProductsAddedSuccessfully = true;

                        // First, add any custom products to the database
                        foreach (var item in ActiveCart.Items)
                        {
                            if (item.Product.Id < 0)
                            {
                                // Ensure the product exists in the database with its negative ID
                                var existingProduct = dbContext.Products.Find(item.Product.Id);
                                if (existingProduct == null)
                                {
                                    try
                                    {
                                        // Make sure custom product has all required fields
                                        item.Product.CategoryId = item.Product.CategoryId == 0 ? 1 : item.Product.CategoryId; // Default category ID
                                        item.Product.IsActive = true;
                                        item.Product.CreatedAt = DateTime.Now;

                                        // Add the custom product to the database - we only track it for this sale
                                        dbContext.Products.Add(item.Product);
                                        dbContext.SaveChanges(); // Save each custom product individually

                                        // Track that this product was added
                                        addedCustomProductIds.Add(item.Product.Id);
                                    }
                                    catch (Exception ex)
                                    {
                                        Debug.WriteLine($"Error saving custom product: {ex.Message}");
                                        customProductsAddedSuccessfully = false;
                                        // We'll handle this in the sale item creation
                                    }
                                }
                                else
                                {
                                    // Product already exists
                                    addedCustomProductIds.Add(item.Product.Id);
                                }
                            }
                        }

                        // Now create all SaleItems
                        List<SaleItem> saleItems = new List<SaleItem>();

                        foreach (var item in ActiveCart.Items)
                        {
                            SaleItem saleItem;

                            if (item.Product.Id < 0 && !addedCustomProductIds.Contains(item.Product.Id))
                            {
                                // This custom product wasn't successfully added to the database
                                // Use a placeholder product ID and make sure we add notes later
                                // Get a valid product ID to use (first product in the same category or ID 1)
                                int placeholderId = 1; // Default fallback

                                try
                                {
                                    var placeholder = dbContext.Products
                                        .Where(p => p.Id > 0)
                                        .OrderBy(p => p.Id)
                                        .Select(p => p.Id)
                                        .FirstOrDefault();

                                    if (placeholder > 0)
                                    {
                                        placeholderId = placeholder;
                                    }
                                }
                                catch
                                {
                                    // Just use the default
                                }

                                saleItem = new SaleItem
                                {
                                    ProductId = placeholderId,
                                    Quantity = item.Quantity,
                                    UnitPrice = item.UnitPrice,
                                    Total = item.Total,
                                    ActualCostBasis = 0 // Custom products have no cost basis
                                };
                            }
                            else
                            {
                                // Regular product or a custom product that was added successfully
                                // Calculate the FIFO cost basis for accurate profit calculations
                                decimal actualCostBasis = item.Product.CalculateFIFOCostBasis(item.Quantity);

                                saleItem = new SaleItem
                                {
                                    ProductId = item.Product.Id,
                                    Quantity = item.Quantity,
                                    UnitPrice = item.UnitPrice,
                                    Total = item.Total,
                                    ActualCostBasis = actualCostBasis
                                };
                            }

                            saleItems.Add(saleItem);
                        }

                        // Assign items to the sale
                        sale.Items = saleItems;

                        // Save the sale to database
                        dbContext.Sales.Add(sale);
                        dbContext.SaveChanges();

                        // Add cash transaction if payment method is Cash
                        if (paymentMethod == "Cash" && currentDrawer != null)
                        {
                            var cashTransaction = new CashTransaction
                            {
                                Type = "Sale",
                                Amount = GrandTotal,
                                Reason = $"Sale {sale.InvoiceNumber}",
                                Reference = sale.InvoiceNumber,
                                Notes = $"Cash sale payment",
                                Timestamp = DateTime.Now,
                                PerformedById = currentUser.Id,
                                CashDrawerId = currentDrawer.Id
                            };

                            dbContext.CashTransactions.Add(cashTransaction);
                            dbContext.SaveChanges();
                        }

                        // Update product stock quantities using proper batch-aware logic
                        foreach (var item in ActiveCart.Items)
                        {
                            // Skip custom products (negative IDs) and services for stock update
                            if (item.Product.Id < 0 || item.Product.Type == ProductType.Service)
                                continue;

                            System.Diagnostics.Debug.WriteLine($"[SALE_STOCK] Processing stock deduction for product: {item.Product.Name} (ID: {item.Product.Id}), Quantity: {item.Quantity}");

                            // Use proper batch-aware stock deduction within the same transaction
                            await DeductStockForSaleInTransaction(dbContext, item.Product.Id, item.Quantity, sale.InvoiceNumber);

                            // Get updated product with latest stock information from the same context
                            var updatedProduct = await dbContext.Products
                                .Include(p => p.Batches)
                                .FirstOrDefaultAsync(p => p.Id == item.Product.Id);

                            if (updatedProduct != null)
                            {
                                // Update the cart item's product reference to match the database
                                item.Product.StockQuantity = updatedProduct.StockQuantity;

                                // Notify about stock change immediately with accurate stock quantity
                                var finalStockQuantity = updatedProduct.TrackBatches ? updatedProduct.GetTotalStockDecimal() : updatedProduct.StockQuantity;
                                NotifyProductStockChanged(item.Product.Id, finalStockQuantity, this);

                                System.Diagnostics.Debug.WriteLine($"[SALE_STOCK] Stock updated for {item.Product.Name}: New stock = {finalStockQuantity}");
                            }
                        }
                        dbContext.SaveChanges();

                        // Add loyalty points if customer is selected
                        if (SelectedCustomer != null && SelectedCustomer.Id != -1 && LoyaltyPointsEarned > 0)
                        {
                            var customer = dbContext.Customers.Find(SelectedCustomer.Id);
                            if (customer != null)
                            {
                                // Add loyalty transaction
                                var loyaltyTransaction = new LoyaltyTransaction
                                {
                                    CustomerId = customer.Id,
                                    Points = LoyaltyPointsEarned,
                                    Description = $"Points earned from sale {sale.InvoiceNumber}",
                                    TransactionDate = DateTime.Now
                                };
                                dbContext.LoyaltyTransactions.Add(loyaltyTransaction);

                                // Update customer points
                                customer.LoyaltyPoints += LoyaltyPointsEarned;
                                dbContext.Update(customer);
                                dbContext.SaveChanges();
                            }
                        }

                        // Handle loyalty points redemption if pending
                        if (CurrentCart.PendingLoyaltyPoints > 0 && SelectedCustomer != null)
                        {
                            var customer = dbContext.Customers.Find(SelectedCustomer.Id);
                            if (customer != null)
                            {
                                // Add redemption transaction
                                var loyaltyTransaction = new LoyaltyTransaction
                                {
                                    CustomerId = customer.Id,
                                    Points = -CurrentCart.PendingLoyaltyPoints,
                                    Description = $"Points redeemed for discount: {Application.Current.TryFindResource("CurrencySymbol") as string ?? "$"}{CurrentCart.PendingLoyaltyDiscount:N2}",
                                    TransactionDate = DateTime.Now
                                };
                                dbContext.LoyaltyTransactions.Add(loyaltyTransaction);

                                // Update customer points
                                customer.LoyaltyPoints -= CurrentCart.PendingLoyaltyPoints;
                                dbContext.Update(customer);
                                dbContext.SaveChanges();
                            }
                        }

                        // Commit the transaction
                        transaction.Commit();

                        // Clear discounts and cart after successful sale
                        ActiveCart.ClearAllDiscounts();
                        ActiveCart.Items.Clear();
                        SelectedCustomer = null;

                        // Force UI update
                        OnPropertyChanged(nameof(Subtotal));
                        OnPropertyChanged(nameof(DiscountAmount));
                        OnPropertyChanged(nameof(TaxAmount));
                        OnPropertyChanged(nameof(GrandTotal));

                        // Play payment completion success sound
                        _soundService.PlaySuccessSound();

                        // Raise the SaleCompleted event
                        SaleCompleted?.Invoke(this, EventArgs.Empty);

                        // Notify SalesHistoryViewModel about the new sale
                        SalesHistoryViewModel.NotifyNewSale();

                        return true;
                    }
                    catch (Exception ex)
                    {
                        // Rollback on error
                        transaction.Rollback();

                        MessageBox.Show($"Error processing payment: {ex.Message}\n\nDetails: {ex.InnerException?.Message ?? "No additional details."}",
                            "Transaction Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error processing payment: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }



        public async Task SelectCustomerAsync()
        {
            try
            {
                var dialogInstance = new Views.Dialogs.CustomerDialog();
                var result = await MaterialDesignThemes.Wpf.DialogHost.Show(dialogInstance, "RootDialog");

                if (result is Customer selectedCustomer)
                {
                    SelectedCustomer = selectedCustomer;
                    UpdateLoyaltyInfo();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error selecting customer: {ex.Message}");
            }
        }

        /// <summary>
        /// ✅ PERFORMANCE FIX: Made async to use async database methods
        /// </summary>
        public async Task SearchLoyaltyCardAsync(string loyaltyCode)
        {
            try
            {
                Debug.WriteLine($"[CART DEBUG] SearchLoyaltyCard called with code: {loyaltyCode}");

                if (string.IsNullOrWhiteSpace(loyaltyCode))
                {
                    Debug.WriteLine("[CART DEBUG] Empty loyalty code provided");
                    return;
                }

                // ✅ PERFORMANCE FIX: Use async version to prevent UI thread blocking
                var customer = await _dbService.GetCustomerByLoyaltyCodeAsync(loyaltyCode);

                if (customer != null)
                {
                    SelectedCustomer = customer;
                    UpdateLoyaltyInfo();

                    Debug.WriteLine($"[CART DEBUG] Customer found by loyalty code: {customer.FullName}");
                    System.Windows.MessageBox.Show($"Customer found: {customer.FullName}", "Loyalty Search",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
                else
                {
                    Debug.WriteLine("[CART DEBUG] No customer found with provided loyalty code");
                    System.Windows.MessageBox.Show("No customer found with the provided loyalty code.", "Loyalty Search",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[CART DEBUG] Error in SearchLoyaltyCard: {ex.Message}");
                System.Windows.MessageBox.Show($"Error searching for customer: {ex.Message}", "Error",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Legacy synchronous method for backward compatibility with XAML bindings
        /// </summary>
        public void SearchLoyaltyCard(string loyaltyCode)
        {
            _ = Task.Run(async () => await SearchLoyaltyCardAsync(loyaltyCode));
        }

        private void RedeemPoints()
        {
            if (SelectedCustomer == null || !HasLoyaltyCustomer || !CanRedeemPoints)
                return;

            var loyaltyProgram = GetActiveLoyaltyProgram();
            if (loyaltyProgram == null)
                return;

            // The dialog will be shown by the view's event handler
            // The view will handle the redemption process using the ViewModel's methods
        }

        public LoyaltyProgram GetActiveLoyaltyProgram()
        {
            return _dbService.GetActiveLoyaltyProgram();
        }

        public void RedeemLoyaltyPoints(decimal points, decimal discountValue)
        {
            _customerService.AddLoyaltyTransaction(
                SelectedCustomer,
                -points,
                $"Points redeemed for discount: {Application.Current.TryFindResource("CurrencySymbol") as string ?? "$"}{discountValue:N2}"
            );
            UpdateLoyaltyInfo();
        }

        public void UpdateLoyaltyInfo()
        {
            if (SelectedCustomer != null && SelectedCustomer.Id != -1 && CurrentCart != null)
            {
                HasLoyaltyCustomer = true;
                CustomerLoyaltyPoints = SelectedCustomer.LoyaltyPoints;

                var loyaltyProgram = _dbService.GetActiveLoyaltyProgram();
                if (loyaltyProgram != null)
                {
                    var customerTier = loyaltyProgram.Tiers
                        .OrderByDescending(t => t.MinimumPoints)
                        .FirstOrDefault(t => SelectedCustomer.LoyaltyPoints >= t.MinimumPoints);

                    decimal pointMultiplier = customerTier?.PointsMultiplier ?? 1;
                    // Calculate points based on the total amount instead of quantity
                    LoyaltyPointsEarned = Math.Floor(CurrentCart.GrandTotal * loyaltyProgram.PointsPerDollar * pointMultiplier);

                    // Update redemption eligibility
                    CanRedeemPoints = CustomerLoyaltyPoints >= loyaltyProgram.MinimumPointsRedemption
                        && CurrentCart.Items.Any()
                        && CurrentCart.GrandTotal > 0
                        && !CurrentCart.Discounts.Any(d => d.ReasonId == 7); // Check if loyalty discount not already applied
                }
            }
            else
            {
                HasLoyaltyCustomer = false;
                CustomerLoyaltyPoints = 0;
                LoyaltyPointsEarned = 0;
                CanRedeemPoints = false;
            }

            OnPropertyChanged(nameof(HasLoyaltyCustomer));
            OnPropertyChanged(nameof(CustomerLoyaltyPoints));
            OnPropertyChanged(nameof(LoyaltyPointsEarned));
            OnPropertyChanged(nameof(CanRedeemPoints));
        }

        public async Task RefreshProducts()
        {
            using var __perfRefresh = new POSSystem.Helpers.PerfTimer("Products.Refresh");
                Debug.WriteLine($"[CART DEBUG] RefreshProducts called. ShowingFavorites={ShowingFavorites}, ShowingPopularItems={ShowingPopularItems}");

            // ✅ CRITICAL FIX: Add emergency timeout protection to prevent UI blocking
            try
            {
                using (var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5))) // 5 second max
                {
                    var refreshTask = Task.Run(async () =>
                    {
                        if (ShowingFavorites)
                        {
                            await LoadFavoriteProducts();
                        }
                        else if (ShowingPopularItems)
                        {
                            await LoadPopularProducts();
                        }
                        else
                        {
                            // Default to loading favorites if neither is set
                            Debug.WriteLine("[CART DEBUG] Neither favorites nor popular items set, defaulting to favorites");
                            _showingFavorites = true;
                            OnPropertyChanged(nameof(ShowingFavorites));
                            await LoadFavoriteProducts();
                        }
                    }, cts.Token);

                    await refreshTask;
                    Debug.WriteLine("[CART DEBUG] RefreshProducts completed successfully");
                }
            }
            catch (OperationCanceledException)
            {
                Debug.WriteLine("🚨 [CART DEBUG] RefreshProducts timed out after 5 seconds - preventing UI blocking");

                // Ensure UI has minimal products to prevent blank screen
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    if (FilteredProducts.Count == 0)
                    {
                        // Show loading indicator or minimal fallback
                        IsLoading = false;
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[CART DEBUG] RefreshProducts error: {ex.Message}");
            }
        }

        public async Task LoadMoreProducts(CancellationToken cancellationToken = default)
        {
            if (_isLoadingMore || !_hasMoreItems) return;

            _isLoadingMore = true;
            try
            {
                var newProducts = await _dbService.GetProductsAsync(
                    pageSize: BATCH_SIZE,
                    offset: _currentOffset,
                    categoryId: SelectedCategory?.Id,
                    searchText: _currentSearchText,
                    cancellationToken: cancellationToken
                );

                if (newProducts.Any())
                {
                    // ✅ FIX: Ensure UI updates happen on the UI thread
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        foreach (var product in newProducts)
                        {
                            FilteredProducts.Add(product);
                        }
                    });
                    _currentOffset += BATCH_SIZE;
                }
                else
                {
                    _hasMoreItems = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadMoreProducts: {ex.Message}");
                _hasMoreItems = false;
            }
            finally
            {
                _isLoadingMore = false;
            }
        }

        // Reset pagination when filtering changes
        public void ResetProductList()
        {
            _currentOffset = 0;
            _hasMoreItems = true;
            FilteredProducts.Clear();
        }

        // Update the search method to reset pagination
        public async Task FilterProducts(string searchText, bool isBarcodeSearch, CancellationToken ct)
        {
            _lastSearchText = searchText;

            // Barcode-like input: do not filter. Preserve selection and return quickly.
            if (!string.IsNullOrEmpty(searchText) && searchText.All(char.IsDigit))
            {
                var preserved = SelectedCartItem;
                if (searchText.Length >= 8)
                {
                    Debug.WriteLine($"Potential barcode detected: {searchText}");
                }
                if (preserved != null)
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        SelectedCartItem = preserved;
                    }, DispatcherPriority.ApplicationIdle);
                }
                return;
            }

            // Only continue filtering if there's at least one non-digit in the search
            if (string.IsNullOrEmpty(searchText) || !searchText.Any(c => !char.IsDigit(c)))
            {
                return;
            }

            // debounce/reset timer is handled by callers; ensure cancellation observed
            ct.ThrowIfCancellationRequested();

            var currentSelection = SelectedCartItem;

            // Perform search on background thread (reuse SearchProductsInternal if possible)
            var normalized = searchText.Trim();
            var terms = normalized.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            var context = ((DatabaseService)_dbService).Context;
            var query = context.Products
                .AsNoTracking()
                .Include(p => p.Category)
                .Include(p => p.Barcodes)
                .Where(p => p.IsActive);

            foreach (var term in terms)
            {
                var t = term;
                query = query.Where(p =>
                    (p.Name != null && p.Name.ToUpper().Contains(t.ToUpper())) ||
                    (p.SKU != null && p.SKU.ToUpper().Contains(t.ToUpper())) ||
                    (p.Description != null && p.Description.ToUpper().Contains(t.ToUpper())) ||
                    p.Barcodes.Any(b => b.Barcode.ToUpper().Contains(t.ToUpper())));
            }

            if (SelectedCategory != null)
            {
                query = query.Where(p => p.CategoryId == SelectedCategory.Id);
            }

            // Observe cancellation before DB call concludes
            ct.ThrowIfCancellationRequested();
            var products = await query.OrderBy(p => p.Name).Take(PAGE_SIZE).ToListAsync(ct);
            ct.ThrowIfCancellationRequested();

            await Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var limited = products.Take(MAX_FILTERED_PRODUCTS).ToList();
                PerformanceHelper.BatchUpdate(() =>
                {
                    // Only update FilteredProducts; keep AllProducts stable to avoid full grid resets
                    CollectionDiffer.ApplyByKey(FilteredProducts, limited, p => p.Id);
                });

                if (currentSelection != null)
                {
                    SelectedCartItem = currentSelection;
                }
            }, DispatcherPriority.Background);
        }

        // Helper method to update product favorite status
        private async void UpdateProductFavoriteStatus(Product product)
        {
            if (CurrentUser == null || product == null) return;

            try
            {
                var context = ((DatabaseService)_dbService).Context;
                {
                    // Check if this product is in user favorites
                    var currentUserId = CurrentUser?.Id;
                    var isFavorited = false;
                    if (currentUserId.HasValue)
                    {
                        isFavorited = await context.UserFavorites
                            .AsNoTracking()
                            .AnyAsync(f => f.UserId == currentUserId.Value && f.ProductId == product.Id);
                    }

                    // Update the product's favorited status
                    product.IsFavorited = isFavorited;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking favorite status: {ex.Message}");
                // Default to not favorited in case of error
                product.IsFavorited = false;
            }
        }

        // ✅ CRITICAL FIX: Add public SearchProducts method for external calls
        public async Task SearchProducts(CancellationToken ct)
        {
            if (string.IsNullOrWhiteSpace(SearchText))
            {
                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    PerformanceHelper.BatchUpdate(() =>
                    {
                        FilteredProducts.Clear();
                        ShowingPopularItems = true;
                    });
                }, System.Windows.Threading.DispatcherPriority.Background);
                return;
            }

            try
            {
                IsSearching = true;
                _currentPage = 0;
                _hasMoreItems = true;
                ShowingPopularItems = false;
                ShowingFavorites = false;

                var normalizedSearch = SearchText.Trim();
                var searchTerms = normalizedSearch.Split(' ', StringSplitOptions.RemoveEmptyEntries);

                var context = ((DatabaseService)_dbService).Context;

                // ✅ SEARCH FIX: Enhanced search with relevance scoring
                var baseQuery = context.Products
                    .AsNoTracking()
                    .Include(p => p.Category)
                    .Include(p => p.Barcodes)
                    .Where(p => p.IsActive && p.Id > 0); // Exclude custom products from search

                if (SelectedCategory != null)
                {
                    baseQuery = baseQuery.Where(p => p.CategoryId == SelectedCategory.Id);
                }

                // ✅ SEARCH FIX: Improved search with exact matches prioritized
                var exactMatches = new List<Product>();
                var partialMatches = new List<Product>();

                // First, try exact matches for better accuracy
                var exactQuery = baseQuery.Where(p =>
                    (p.Name != null && p.Name.ToUpper() == normalizedSearch.ToUpper()) ||
                    (p.SKU != null && p.SKU.ToUpper() == normalizedSearch.ToUpper()) ||
                    p.Barcodes.Any(b => b.Barcode.ToUpper() == normalizedSearch.ToUpper()));

                ct.ThrowIfCancellationRequested();
                exactMatches = await exactQuery.Take(5).ToListAsync(ct);

                // Then, get partial matches if we need more results
                if (exactMatches.Count < PAGE_SIZE)
                {
                    var partialQuery = baseQuery.Where(p => !exactMatches.Select(em => em.Id).Contains(p.Id));

                    foreach (var term in searchTerms)
                    {
                        var searchTerm = term.ToUpper();
                        partialQuery = partialQuery.Where(p =>
                            (p.Name != null && p.Name.ToUpper().Contains(searchTerm)) ||
                            (p.SKU != null && p.SKU.ToUpper().Contains(searchTerm)) ||
                            (p.Description != null && p.Description.ToUpper().Contains(searchTerm)) ||
                            p.Barcodes.Any(b => b.Barcode.ToUpper().Contains(searchTerm)));
                    }

                    ct.ThrowIfCancellationRequested();
                    partialMatches = await partialQuery
                        .OrderBy(p => p.Name.ToUpper().StartsWith(normalizedSearch.ToUpper()) ? 0 : 1) // Prioritize name starts with
                        .ThenBy(p => p.Name)
                        .Take(PAGE_SIZE - exactMatches.Count)
                        .ToListAsync(ct);
                }

                // Combine results with exact matches first
                var products = exactMatches.Concat(partialMatches).ToList();
                ct.ThrowIfCancellationRequested();

                var limitedProducts = products.Take(MAX_FILTERED_PRODUCTS).ToList();
                using (new PerfTimer("[UI] SearchProducts ApplyByKey"))
                {
                    await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        PerformanceHelper.BatchUpdate(() =>
                        {
                            CollectionDiffer.ApplyByKey(FilteredProducts, limitedProducts, p => p.Id);
                        });
                    }, System.Windows.Threading.DispatcherPriority.Background);
                }
            }
            catch (OperationCanceledException)
            {
                // Expected on fast typing; no user-visible error
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SEARCH] Error searching products: {ex.Message}");
            }
            finally
            {
                IsSearching = false;
            }
        }

        // Back-compat overloads for callers not yet updated
        public Task SearchProducts() => SearchProducts(CancellationToken.None);
        public Task FilterProducts(string searchText, bool isBarcodeSearch = false)
            => FilterProducts(searchText, isBarcodeSearch, CancellationToken.None);

        /// <summary>
        /// Test method to verify search functionality is working
        /// </summary>
        public async Task<bool> TestSearchFunctionality()
        {
            try
            {
                Debug.WriteLine("[SEARCH_TEST] Starting search functionality test...");

                var context = ((DatabaseService)_dbService).Context;

                // Test 1: Count all active products
                var totalProducts = await context.Products.CountAsync(p => p.IsActive);
                Debug.WriteLine($"[SEARCH_TEST] Total active products in database: {totalProducts}");

                // Test 2: Count weight-based products
                var weightBasedCount = await context.Products.CountAsync(p => p.IsActive && p.IsWeightBased);
                Debug.WriteLine($"[SEARCH_TEST] Weight-based products: {weightBasedCount}");

                // Test 3: Try a simple search
                var testProducts = await context.Products
                    .Where(p => p.IsActive && p.Name != null && p.Name != "")
                    .Take(5)
                    .ToListAsync();

                Debug.WriteLine($"[SEARCH_TEST] Sample products for testing:");
                foreach (var product in testProducts)
                {
                    Debug.WriteLine($"[SEARCH_TEST]   - ID: {product.Id}, Name: '{product.Name}', SKU: '{product.SKU}', IsWeightBased: {product.IsWeightBased}");
                }

                return totalProducts > 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[SEARCH_TEST] Error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Test method to verify stock status calculation for weight-based products
        /// </summary>
        public async Task TestStockStatusCalculation()
        {
            try
            {
                Debug.WriteLine("[STOCK_TEST] Starting stock status calculation test...");

                var context = ((DatabaseService)_dbService).Context;

                // Get weight-based products for testing
                var weightBasedProducts = await context.Products
                    .Where(p => p.IsActive && p.IsWeightBased)
                    .Take(5)
                    .ToListAsync();

                Debug.WriteLine($"[STOCK_TEST] Testing stock status for {weightBasedProducts.Count} weight-based products:");

                foreach (var product in weightBasedProducts)
                {
                    Debug.WriteLine($"[STOCK_TEST] Product {product.Id} ({product.Name}):");
                    Debug.WriteLine($"[STOCK_TEST]   - StockQuantity: {product.StockQuantity}");
                    Debug.WriteLine($"[STOCK_TEST]   - MinimumStock: {product.MinimumStock}");
                    Debug.WriteLine($"[STOCK_TEST]   - GetTotalStock(): {product.GetTotalStock()}");
                    Debug.WriteLine($"[STOCK_TEST]   - IsOutOfStock: {product.IsOutOfStock}");
                    Debug.WriteLine($"[STOCK_TEST]   - IsLowStock: {product.IsLowStock}");
                    Debug.WriteLine($"[STOCK_TEST]   - IsInStock: {product.IsInStock}");
                    Debug.WriteLine($"[STOCK_TEST]   - StockStatus: {product.StockStatus}");
                    Debug.WriteLine($"[STOCK_TEST]   ---");
                }

                // Test specific product 483 if it exists
                var product483 = await context.Products.FirstOrDefaultAsync(p => p.Id == 483);
                if (product483 != null)
                {
                    Debug.WriteLine($"[STOCK_TEST] Specific test for Product 483:");
                    Debug.WriteLine($"[STOCK_TEST]   - Name: {product483.Name}");
                    Debug.WriteLine($"[STOCK_TEST]   - StockQuantity: {product483.StockQuantity}");
                    Debug.WriteLine($"[STOCK_TEST]   - IsWeightBased: {product483.IsWeightBased}");
                    Debug.WriteLine($"[STOCK_TEST]   - Type: {product483.Type}");
                    Debug.WriteLine($"[STOCK_TEST]   - IsOutOfStock: {product483.IsOutOfStock}");
                    Debug.WriteLine($"[STOCK_TEST]   - IsInStock: {product483.IsInStock}");
                    Debug.WriteLine($"[STOCK_TEST]   - StockStatus: {product483.StockStatus}");
                }
                else
                {
                    Debug.WriteLine($"[STOCK_TEST] Product 483 not found in database");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[STOCK_TEST] Error: {ex.Message}");
            }
        }

        private async Task<List<Product>> SearchProducts(string searchText, bool isBarcodeSearch = false)
        {
            if (string.IsNullOrWhiteSpace(searchText))
                return new List<Product>();

            // Normalize search text
            var normalizedSearch = searchText.Trim().ToUpperInvariant();

            // Check cache first
            if (_searchCache.TryGetValue(normalizedSearch, out var cachedResult))
            {
                if (DateTime.Now.Subtract(cachedResult.Timestamp).TotalMinutes < CACHE_EXPIRY_MINUTES)
                    return cachedResult.Products;
                _searchCache.Remove(normalizedSearch);
            }

            try
            {
                var context = ((DatabaseService)_dbService).Context;
                // ✅ SEARCH FIX: Enhanced search with custom product exclusion and relevance
                var baseQuery = context.Products
                    .AsNoTracking()
                    .Where(p => p.IsActive && p.Id > 0) // ✅ SEARCH FIX: Exclude custom products (negative IDs)
                    .Select(p => new
                    {
                        p.Id,
                        p.Name,
                        p.SKU,
                        p.CategoryId,
                        CategoryName = p.Category.Name,
                        p.SellingPrice,
                        p.StockQuantity,
                        p.ReorderPoint,
                        p.IsActive,
                        p.IsWeightBased,
                        p.Type,
                        p.TrackBatches,
                        Barcodes = p.Barcodes.Select(b => b.Barcode).ToList()
                    });

                var query = baseQuery;

                if (isBarcodeSearch)
                {
                    // ✅ SEARCH FIX: Exact match for barcodes with better ordering
                    query = query.Where(p => p.Barcodes.Contains(normalizedSearch))
                                 .OrderBy(p => p.Name);
                }
                else
                {
                    // ✅ SEARCH FIX: Enhanced search with relevance scoring
                    var searchTerms = normalizedSearch.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                    var upperSearch = normalizedSearch.ToUpper();

                    // Build query with relevance-based ordering
                    foreach (var term in searchTerms)
                    {
                        var searchTerm = term.ToUpper();
                        query = query.Where(p =>
                            EF.Functions.Like(p.Name.ToUpper(), $"%{searchTerm}%") ||
                            (p.SKU != null && EF.Functions.Like(p.SKU.ToUpper(), $"%{searchTerm}%")) ||
                            p.Barcodes.Any(b => EF.Functions.Like(b.ToUpper(), $"%{searchTerm}%"))
                        );
                    }

                    // ✅ SEARCH FIX: Order by relevance - exact matches first, then starts with, then contains
                    query = query.OrderBy(p =>
                        p.Name.ToUpper() == upperSearch ? 0 :
                        p.Name.ToUpper().StartsWith(upperSearch) ? 1 :
                        (p.SKU != null && p.SKU.ToUpper() == upperSearch) ? 2 :
                        (p.SKU != null && p.SKU.ToUpper().StartsWith(upperSearch)) ? 3 : 4)
                        .ThenBy(p => p.Name);
                }

                // Execute optimized query and map to Product objects
                var results = await query
                    .Take(SEARCH_PAGE_SIZE)
                    .ToListAsync();

                var products = results.Select(p => {
                    return new Product
                    {
                        Id = p.Id,
                        Name = p.Name,
                        SKU = p.SKU,
                        CategoryId = p.CategoryId,
                        Category = new Category { Id = p.CategoryId, Name = p.CategoryName },
                        SellingPrice = p.SellingPrice,
                        StockQuantity = p.StockQuantity,
                        ReorderPoint = p.ReorderPoint,
                        IsActive = p.IsActive,
                        IsWeightBased = p.IsWeightBased,
                        Type = p.Type,
                        TrackBatches = p.TrackBatches,
                        Barcodes = p.Barcodes.Select(b => new ProductBarcode { Barcode = b }).ToList()
                    };
                }).ToList();

                // Cache the results with size management
                if (_searchCache.Count >= CACHE_SIZE)
                {
                    var oldestKey = _searchCache
                        .OrderBy(x => x.Value.Timestamp)
                        .First().Key;
                    _searchCache.Remove(oldestKey);
                }
                _searchCache[normalizedSearch] = (products, DateTime.Now);

                return products;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Search error: {ex.Message}");
                return new List<Product>();
            }
        }

        public void ApplyLoyaltyDiscount(decimal discountValue)
        {
            if (CurrentCart == null) return;

            // Clear any existing loyalty discounts
            var existingLoyaltyDiscounts = CurrentCart.Discounts
                .Where(d => d.ReasonId == 7) // Loyalty Discount reason
                .ToList();
            foreach (var oldDiscount in existingLoyaltyDiscounts)
            {
                CurrentCart.Discounts.Remove(oldDiscount);
            }

            // Create and apply the loyalty discount
            var discount = new Discount
            {
                DiscountTypeId = 2, // Fixed Amount type
                DiscountValue = discountValue,
                ReasonId = 7, // Loyalty Discount reason
                AppliedByUserId = CurrentUser?.Id ?? 1,
                Comment = $"Loyalty points redemption: ${discountValue:N2}",
                DiscountType = new DiscountType { Id = 2, Name = "Fixed Amount" }
            };

            CurrentCart.Discounts.Add(discount);
            CurrentCart.PendingLoyaltyDiscount = discountValue;

            // Force update all totals
            CurrentCart.UpdateTotals();
            CalculateTotals(); // This will trigger all necessary property updates
        }

        public async Task<Customer> SearchCustomerByLoyaltyCode(string loyaltyCode)
        {
            if (string.IsNullOrWhiteSpace(loyaltyCode))
                return null;

            try
            {
                var context = ((DatabaseService)_dbService).Context;
                {
                    // Search for customers by loyalty card number
                    var customer = await context.Customers
                        .AsNoTracking()
                        .FirstOrDefaultAsync(c => c.LoyaltyCode == loyaltyCode);

                    return customer;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error searching for customer by loyalty code: {ex.Message}");
                throw;
            }
        }

        public Product GetProductByBarcode(string barcode)
        {
            if (string.IsNullOrWhiteSpace(barcode))
                return null;

            // Check cache first
            if (_barcodeCache.TryGetValue(barcode, out var cachedResult))
            {
                if (DateTime.Now.Subtract(cachedResult.Timestamp).TotalMinutes < BARCODE_CACHE_EXPIRY_MINUTES)
                {
                    Debug.WriteLine("Barcode cache hit");
                    return cachedResult.Product;
                }
                _barcodeCache.Remove(barcode);
            }

            try
            {
                // Fetch from database with optimized query
                var product = _dbService.GetProductByBarcode(barcode);

                // ✅ FIX: For batch-tracked products, ensure we have the latest batch data for accurate stock
                if (product != null && product.TrackBatches)
                {
                    var batches = _dbService.GetBatchesForProduct(product.Id);
                    product.Batches = batches;
                    System.Diagnostics.Debug.WriteLine($"[BARCODE_LOOKUP] Loaded {batches.Count} batches for product {product.Name}, total stock: {product.GetTotalStockDecimal()}");
                }

                // Add to cache if found
                if (product != null)
                {
                    // Keep cache size manageable (remove oldest entries if too large)
                    if (_barcodeCache.Count > 1000)
                    {
                        var oldestKey = _barcodeCache.OrderBy(x => x.Value.Timestamp).First().Key;
                        _barcodeCache.Remove(oldestKey);
                    }

                    _barcodeCache[barcode] = (product, DateTime.Now);
                }

                return product;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting product by barcode: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Enhanced barcode lookup that includes external ProductsDB search and import functionality
        /// </summary>
        public async Task<Product> GetProductByBarcodeWithExternalLookupAsync(string barcode)
        {
            if (string.IsNullOrWhiteSpace(barcode))
                return null;

            // First try the main database (using existing method)
            var product = GetProductByBarcode(barcode);
            if (product != null)
                return product;

            // If not found in main database, try external ProductsDB
            try
            {
                // Initialize external product lookup service
                var alertService = new SimpleAlertService();
                var productLookupService = new ProductLookupService(alertService);

                var externalProduct = await productLookupService.LookupProductByBarcodeAsync(barcode);
                if (externalProduct != null)
                {
                    // Show import dialog to user
                    var importDialog = new Views.Dialogs.ExternalProductImportDialog(externalProduct, barcode);
                    var result = await ShowExternalProductImportDialog(importDialog);

                    if (result is Views.Dialogs.ExternalProductImportDialog importDialogResult && importDialogResult.OpenAddProductDialog)
                    {
                        // Open the Add Product dialog with pre-filled data
                        var addedProduct = await OpenAddProductDialogWithExternalData(externalProduct, barcode);
                        if (addedProduct != null)
                        {
                            // Clear cache to ensure fresh data
                            if (_barcodeCache.ContainsKey(barcode))
                                _barcodeCache.Remove(barcode);

                            return addedProduct;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error during external product lookup: {ex.Message}");
                MessageBox.Show($"Error searching external database: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }

            return null;
        }

        private async Task<object> ShowExternalProductImportDialog(Views.Dialogs.ExternalProductImportDialog importDialog)
        {
            // Try different DialogHost identifiers in order of preference
            string[] dialogIdentifiers = { "SalesDialog", "MainSalesDialog", "MainDialog" };

            foreach (string identifier in dialogIdentifiers)
            {
                try
                {
                    return await MaterialDesignThemes.Wpf.DialogHost.Show(importDialog, identifier);
                }
                catch (InvalidOperationException ex) when (ex.Message.Contains("DialogHost") && ex.Message.Contains("Identifier"))
                {
                    // This DialogHost identifier doesn't exist, try the next one
                    continue;
                }
            }

            // If all DialogHost attempts fail, fall back to a simple MessageBox
            var result = MessageBox.Show(
                $"Product found in external database:\n\n" +
                $"Name: {importDialog.ExternalProduct.Name}\n" +
                $"Price: {importDialog.ExternalProduct.SellingPrice:C}\n" +
                $"Description: {importDialog.ExternalProduct.Description}\n\n" +
                $"Would you like to import this product?",
                "Product Found in External Database",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            return result == MessageBoxResult.Yes;
        }

        private async Task<Product> ImportExternalProductAsync(Product externalProduct, string barcode)
        {
            try
            {
                // Create a new product based on external data
                var newProduct = new Product
                {
                    Name = externalProduct.Name,
                    Description = externalProduct.Description ?? string.Empty,
                    SKU = GenerateUniqueSKU(externalProduct.Name),
                    PurchasePrice = externalProduct.PurchasePrice,
                    SellingPrice = externalProduct.SellingPrice,
                    DefaultPrice = externalProduct.SellingPrice, // Set DefaultPrice to SellingPrice
                    StockQuantity = 0, // Start with 0 stock - user needs to add inventory
                    MinimumStock = 10, // Default minimum stock
                    ReorderPoint = 5, // Default reorder point
                    LoyaltyPoints = 0, // Default loyalty points
                    IsActive = true,
                    ImageData = externalProduct.ImageData,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                // Set category if available
                var dbService = _dbService as DatabaseService;
                if (dbService != null)
                {
                    if (externalProduct.Category != null && !string.IsNullOrWhiteSpace(externalProduct.Category.Name))
                    {
                        // Try to find existing category or create new one
                        var existingCategory = dbService.GetCategoryByName(externalProduct.Category.Name);
                        if (existingCategory != null)
                        {
                            newProduct.CategoryId = existingCategory.Id;
                        }
                        else
                        {
                            // Create new category
                            var newCategory = new Category
                            {
                                Name = externalProduct.Category.Name,
                                IsActive = true
                            };
                            dbService.AddCategory(newCategory);
                            newProduct.CategoryId = newCategory.Id;
                        }
                    }
                    else
                    {
                        // Fallback: Use default "Imported" category
                        var defaultCategory = dbService.GetCategoryByName("Imported") ??
                                            dbService.GetCategoryByName("General") ??
                                            dbService.GetCategoryByName("Uncategorized");

                        if (defaultCategory != null)
                        {
                            newProduct.CategoryId = defaultCategory.Id;
                        }
                        else
                        {
                            // Create default category if none exists
                            var importedCategory = new Category
                            {
                                Name = "Imported",
                                Description = "Products imported from external database",
                                IsActive = true
                            };
                            dbService.AddCategory(importedCategory);
                            newProduct.CategoryId = importedCategory.Id;
                        }
                    }
                }

                // Add the product to the database
                _dbService.AddProduct(newProduct);
                var productId = newProduct.Id; // AddProduct sets the ID on the product object

                // Add the barcode - cast to concrete DatabaseService
                var concreteDbService = _dbService as DatabaseService;
                if (concreteDbService != null)
                {
                    concreteDbService.AddBarcodeToProduct(productId, barcode, true, "Imported from external database");
                }

                // Reload the product with full details
                var importedProduct = _dbService.GetProductById(productId);

                if (importedProduct != null)
                {
                    MessageBox.Show($"Product '{importedProduct.Name}' imported successfully!", "Import Successful",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }

                return importedProduct;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error importing external product: {ex.Message}");
                MessageBox.Show($"Error importing product: {ex.Message}", "Import Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }
        }

        private string GenerateUniqueSKU(string productName)
        {
            // Generate a unique SKU based on product name and timestamp
            var baseSku = productName.Length > 3 ? productName.Substring(0, 3).ToUpper() : productName.ToUpper();
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            return $"{baseSku}-{timestamp}";
        }

        // Simple implementation of IAlertService for external product lookup
        private class SimpleAlertService : IAlertService
        {
            public void CheckExpiringProducts() { }
            public void CreateAlert(int referenceId, string alertType, string message, string referenceType = "Product") { }
            public List<ProductAlert> GetAllAlerts(int? limit = null, int page = 1) => new List<ProductAlert>();
            public List<ProductAlert> GetAllAlertsBasic(int? limit = null, int page = 1) => new List<ProductAlert>();
            public int GetTotalAlertsCount() => 0;
            public List<ProductAlert> GetUnreadAlerts() => new List<ProductAlert>();
            public List<ProductAlert> GetUnreadAlertsBasic() => new List<ProductAlert>();
            public int GetUnreadAlertsCount() => 0;
            public void MarkAlertAsRead(int alertId) { }
            public void MarkAllAlertsAsRead() { }
            public void ShowError(string message)
            {
                MessageBox.Show(message, "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            public void ClearAlertCache() { }
        }

        private async Task CreateInvoiceFromOutOfStockProduct(Product product)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] CreateInvoiceFromOutOfStockProduct called for product: {product.Name} (ID: {product.Id})");

                // Get required services using DI to ensure a single, consistent DatabaseService instance
                var dbServiceConcrete = _dbService as POSSystem.Services.DatabaseService
                    ?? App.ServiceProvider?.GetService(typeof(POSSystem.Services.DatabaseService)) as POSSystem.Services.DatabaseService;
                var permissionsService = new POSSystem.Services.UserPermissionsService(_dbService);

                System.Diagnostics.Debug.WriteLine($"[INVOICE] Services obtained - dbServiceConcrete: {dbServiceConcrete != null}, permissionsService: {permissionsService != null}");

                if (permissionsService == null || dbServiceConcrete == null)
                {
                    MessageBox.Show("Required services not available.", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // Create and show confirmation dialog
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Creating ProductToInvoiceConfirmationDialog for product: {product.Name}");
                var confirmationViewModel = new POSSystem.ViewModels.ProductToInvoiceConfirmationViewModel(product, permissionsService, dbServiceConcrete);
                var confirmationDialog = new POSSystem.Views.Dialogs.ProductToInvoiceConfirmationDialog(confirmationViewModel);

                // Show invoice confirmation dialog
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Showing ProductToInvoiceConfirmationDialog...");
                var result = await MaterialDesignThemes.Wpf.DialogHost.Show(confirmationDialog, "SalesDialog");
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Dialog closed. Result: {result}");

                // Prefer the explicit result returned by DialogHost over reading the control's property
                if (result is POSSystem.ViewModels.ProductToInvoiceResult dialogResult && dialogResult.Confirmed)
                {
                    var invoiceResult = dialogResult;
                    System.Diagnostics.Debug.WriteLine($"[INVOICE] Dialog confirmed. CreateFullInvoice: {invoiceResult.CreateFullInvoice}, Quantity: {invoiceResult.Quantity}");

                    if (invoiceResult.CreateFullInvoice)
                    {
                        // Admin user - create full invoice directly
                        System.Diagnostics.Debug.WriteLine($"[INVOICE] Creating full invoice for admin user");
                        await CreateFullInvoiceFromProduct(invoiceResult);
                    }
                    else
                    {
                        // Non-admin user - create draft invoice
                        System.Diagnostics.Debug.WriteLine($"[INVOICE] Creating draft invoice for non-admin user");
                        await CreateDraftInvoiceFromProduct(invoiceResult);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[INVOICE] Dialog was cancelled or not confirmed");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error in CreateInvoiceFromOutOfStockProduct: {ex.Message}");
                MessageBox.Show($"Error creating invoice: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateFullInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult result)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Creating full invoice for product: {result.Product.Name}");

                // Get current user (using test mode for now)
                var currentUser = new User { Id = 1, FirstName = "Test", LastName = "User" };

                // Create invoice using DatabaseService
                var invoice = new Invoice
                {
                    InvoiceNumber = GenerateInvoiceNumber(),
                    Type = "Sales",
                    IssueDate = DateTime.Now,
                    DueDate = DateTime.Now.AddDays(30),
                    CustomerId = result.Customer?.Id,
                    Status = "Issued", // Full invoice is immediately issued
                    PaymentTerms = "Net 30",
                    Reference = $"Out-of-stock order for {result.Product.Name}",
                    Notes = $"Invoice created for out-of-stock product: {result.Product.Name}",
                    CreatedByUserId = currentUser.Id,
                    DraftCreatedAt = DateTime.Now,
                    RequiresAdminCompletion = false
                };

                // Add the product as an invoice item
                var invoiceItem = new InvoiceItem
                {
                    ProductId = result.Product.Id,
                    ProductName = result.Product.Name,
                    Quantity = result.Quantity,
                    UnitPrice = result.Product.TrackBatches
                        ? (_dbService.GetBatchesForProduct(result.Product.Id)
                            .Where(b => b.Quantity > 0)
                            .OrderBy(b => b.CreatedAt)
                            .ThenBy(b => b.Id)
                            .FirstOrDefault()?.SellingPrice ?? result.Product.SellingPrice)
                        : result.Product.SellingPrice,
                    BatchNumber = null
                };
                invoiceItem.CalculateTotal();
                invoice.Items.Add(invoiceItem);

                // Calculate totals
                invoice.CalculateTotals();

                // Save to database
                int invoiceId = _dbService.CreateInvoice(invoice);

                if (invoiceId > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"[INVOICE] Full invoice created successfully with ID: {invoiceId}");
                    MessageBox.Show($"Invoice #{invoice.InvoiceNumber} created successfully for {result.Product.Name}!\n\nQuantity: {result.Quantity}\nTotal: {invoice.GrandTotal:C}",
                        "Invoice Created", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    throw new Exception("Failed to save invoice to database");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error creating full invoice: {ex.Message}");
                MessageBox.Show($"Error creating full invoice: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task CreateDraftInvoiceFromProduct(POSSystem.ViewModels.ProductToInvoiceResult result)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Creating stock reservation invoice for product: {result.Product.Name}");

                // Get current user (using test mode for now)
                var currentUser = new User { Id = 1, FirstName = "Test", LastName = "User" };

                // Create stock reservation invoice using DatabaseService
                var invoice = new Invoice
                {
                    InvoiceNumber = GenerateStockReservationInvoiceNumber(),
                    Type = "Stock Reservation", // New invoice type for stock reservations
                    IssueDate = DateTime.Now,
                    DueDate = DateTime.Now.AddDays(30),
                    CustomerId = result.Customer?.Id,
                    Status = "Draft", // Draft status
                    PaymentTerms = "Net 30",
                    Reference = $"Stock reservation for {result.Product.Name}",
                    Notes = $"Stock reservation invoice created for out-of-stock product: {result.Product.Name}. Stock has been reserved and added to inventory. Requires admin completion for final processing.",
                    CreatedByUserId = currentUser.Id,
                    DraftCreatedAt = DateTime.Now,
                    RequiresAdminCompletion = true // Draft requires admin completion
                };

                // Add the product as an invoice item
                var invoiceItem = new InvoiceItem
                {
                    ProductId = result.Product.Id,
                    ProductName = result.Product.Name,
                    Quantity = result.Quantity,
                    UnitPrice = result.Product.TrackBatches
                        ? (_dbService.GetBatchesForProduct(result.Product.Id)
                            .Where(b => b.Quantity > 0)
                            .OrderBy(b => b.CreatedAt)
                            .ThenBy(b => b.Id)
                            .FirstOrDefault()?.SellingPrice ?? result.Product.SellingPrice)
                        : result.Product.SellingPrice,
                    BatchNumber = null
                };
                invoiceItem.CalculateTotal();
                invoice.Items.Add(invoiceItem);

                // Calculate totals
                invoice.CalculateTotals();

                // Save to database
                int invoiceId = _dbService.CreateInvoice(invoice);

                if (invoiceId > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"[INVOICE] Stock reservation invoice created successfully with ID: {invoiceId}");

                    // IMPORTANT: Add the reserved quantity to the product's stock
                    await AddReservedStockToProduct(result.Product.Id, result.Quantity, invoiceId, currentUser.Id);

                    // Refresh the specific product to show updated stock immediately
                    await RefreshSpecificProduct(result.Product.Id);

                    MessageBox.Show($"Stock Reservation Invoice #{invoice.InvoiceNumber} created successfully!\n\nProduct: {result.Product.Name}\nReserved Quantity: {result.Quantity}\nEstimated Total: {invoice.GrandTotal:C}\n\n✅ Stock has been added to inventory and is now available for sales.\n\nThis reservation requires admin completion for final processing.",
                        "Stock Reservation Created", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    throw new Exception("Failed to save stock reservation invoice to database");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[INVOICE] Error creating stock reservation invoice: {ex.Message}");
                MessageBox.Show($"Error creating stock reservation invoice: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Generates a unique invoice number for full invoices
        /// </summary>
        private string GenerateInvoiceNumber()
        {
            try
            {
                int nextNumber = _dbService.GetNextInvoiceNumber();
                return $"INV-{DateTime.Now:yyyyMMdd}-{nextNumber:D4}";
            }
            catch
            {
                // Fallback to timestamp-based number
                return $"INV-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// Generates a unique draft invoice number
        /// </summary>
        private string GenerateDraftInvoiceNumber()
        {
            try
            {
                int nextNumber = _dbService.GetNextInvoiceNumber();
                return $"DRAFT-{DateTime.Now:yyyyMMdd}-{nextNumber:D4}";
            }
            catch
            {
                // Fallback to timestamp-based number
                return $"DRAFT-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// Generates a unique stock reservation invoice number
        /// </summary>
        private string GenerateStockReservationInvoiceNumber()
        {
            try
            {
                int nextNumber = _dbService.GetNextInvoiceNumber();
                return $"RESERVE-{DateTime.Now:yyyyMMdd}-{nextNumber:D4}";
            }
            catch
            {
                // Fallback to timestamp-based number
                return $"RESERVE-{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// Adds reserved stock to a product and creates an inventory transaction for tracking
        /// </summary>
        private async Task AddReservedStockToProduct(int productId, decimal quantity, int invoiceId, int userId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Adding {quantity} reserved stock to product {productId} for invoice {invoiceId}");

                // Get current product to check if it uses batch tracking
                var currentProduct = _dbService.GetProductById(productId);
                if (currentProduct == null)
                {
                    throw new Exception($"Product with ID {productId} not found");
                }

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Product {productId} TrackBatches: {currentProduct.TrackBatches}");

                decimal newStockQuantity;
                if (currentProduct.TrackBatches)
                {
                    // For batch-tracked products, create a new batch entry
                    await CreateStockReservationBatch(productId, quantity, invoiceId);

                    // ✅ FIX: For batch-tracked products, reload batches and calculate total stock
                    var updatedProduct = _dbService.GetProductById(productId);
                    if (updatedProduct != null)
                    {
                        // Load the latest batch data to get accurate total stock
                        var batches = _dbService.GetBatchesForProduct(productId);
                        updatedProduct.Batches = batches;

                        // Calculate total stock from all batches
                        newStockQuantity = updatedProduct.GetTotalStockDecimal();
                        System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Batch-tracked product {productId}: Total stock after batch creation = {newStockQuantity}");
                    }
                    else
                    {
                        newStockQuantity = currentProduct.StockQuantity + quantity;
                    }
                }
                else
                {
                    // For regular products, update the stock quantity directly
                    newStockQuantity = currentProduct.StockQuantity + quantity;
                    // Prefer delta-based update on concrete service to avoid EF NoTracking issues
                    if (_dbService is POSSystem.Services.DatabaseService concreteDb)
                    {
                        concreteDb.UpdateProductStock(productId, quantity); // delta +quantity
                    }
                    else
                    {
                        _dbService.UpdateProductStock(productId, newStockQuantity, $"Stock reservation from invoice #{invoiceId}");
                    }
                }

                // Create an inventory transaction to track this stock addition
                var inventoryTransaction = new InventoryTransaction
                {
                    ProductId = productId,
                    TransactionType = "Stock Reservation",
                    Quantity = (int)Math.Round(quantity), // Round to int for inventory tracking
                    UnitPrice = 0, // No cost for reservations
                    Reference = $"Invoice #{invoiceId}",
                    TransactionDate = DateTime.Now,
                    Notes = $"Stock reserved via invoice #{invoiceId}. Can be reversed if invoice is cancelled.",
                    UserId = userId
                };

                _dbService.AddInventoryTransaction(inventoryTransaction);

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Created inventory transaction for stock addition: {quantity} units");

                // ✅ FIX: Fire ProductStockChanged event to notify UI components
                NotifyProductStockChanged(productId, newStockQuantity, this);
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Fired ProductStockChanged event for product {productId}, new stock: {newStockQuantity}");

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Successfully added reserved stock and created inventory transaction");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error adding reserved stock: {ex.Message}");
                throw new Exception($"Failed to add reserved stock: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Creates a batch entry for stock reservation on batch-tracked products
        /// </summary>
        private async Task CreateStockReservationBatch(int productId, decimal quantity, int invoiceId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Creating batch entry for product {productId}, quantity: {quantity}");

                // Create a new batch for the reserved stock
                var batchStock = new BatchStock
                {
                    ProductId = productId,
                    BatchNumber = $"RESERVE-{invoiceId}-{DateTime.Now:yyyyMMddHHmmss}",
                    Quantity = quantity,
                    ManufactureDate = DateTime.Now,
                    ExpiryDate = null, // No expiry for reserved stock
                    PurchasePrice = 0, // No cost for reservations
                    Location = "Reserved Stock",
                    Notes = $"Stock reserved via invoice #{invoiceId}",
                    CreatedAt = DateTime.Now
                };

                // Add the batch to the database
                _dbService.AddBatchStock(batchStock);

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Created batch {batchStock.BatchNumber} with {quantity} units");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error creating batch: {ex.Message}");
                throw new Exception($"Failed to create stock reservation batch: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Removes reserved stock from a product when a stock reservation invoice is cancelled
        /// </summary>
        private async Task RemoveReservedStockFromProduct(int productId, decimal quantity, int invoiceId, int userId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Removing {quantity} reserved stock from product {productId} for cancelled invoice {invoiceId}");

                // Get current stock and subtract the reserved quantity
                var currentProduct = _dbService.GetProductById(productId);
                if (currentProduct == null)
                {
                    throw new Exception($"Product with ID {productId} not found");
                }

                var newStockQuantity = Math.Max(0, currentProduct.StockQuantity - quantity); // Prevent negative stock

                // Update the product's stock quantity (set to new total)
                _dbService.UpdateProductStock(productId, newStockQuantity, $"Stock reservation cancelled from invoice #{invoiceId}");

                // Create an inventory transaction to track this stock removal
                var inventoryTransaction = new InventoryTransaction
                {
                    ProductId = productId,
                    TransactionType = "Stock Reservation Cancelled",
                    Quantity = -(int)Math.Round(quantity), // Negative quantity for removal
                    UnitPrice = 0,
                    Reference = $"Invoice #{invoiceId} Cancelled",
                    TransactionDate = DateTime.Now,
                    Notes = $"Stock reservation cancelled for invoice #{invoiceId}. Reserved stock removed from inventory.",
                    UserId = userId
                };

                _dbService.AddInventoryTransaction(inventoryTransaction);

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Created inventory transaction for stock removal: {quantity} units");

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Successfully removed reserved stock and created inventory transaction");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error removing reserved stock: {ex.Message}");
                throw new Exception($"Failed to remove reserved stock: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Refreshes the product data to show updated stock quantities
        /// </summary>
        private async Task RefreshProductData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("[STOCK_RESERVATION] Refreshing product data...");

                // Trigger a refresh of the products collection
                await RefreshProducts();

                System.Diagnostics.Debug.WriteLine("[STOCK_RESERVATION] Product data refreshed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error refreshing product data: {ex.Message}");
                // Don't throw here as this is not critical to the main operation
            }
        }

        /// <summary>
        /// Deducts stock for a sale using proper batch-aware logic within an existing transaction
        /// </summary>
        private async Task DeductStockForSaleInTransaction(POSDbContext dbContext, int productId, decimal quantity, string saleReference)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Starting stock deduction for product {productId}, quantity: {quantity}");

                var product = await dbContext.Products
                    .Include(p => p.Batches)
                    .FirstOrDefaultAsync(p => p.Id == productId);

                if (product == null)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Product {productId} not found");
                    return;
                }

                if (product.TrackBatches)
                {
                    System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Product {product.Name} tracks batches - using FIFO deduction");

                    decimal remainingQuantity = quantity;

                    // Get all batches for this product with available stock, ordered by creation date (FIFO - First In, First Out)
                    var batches = product.Batches
                        .Where(b => b.Quantity > 0)
                        .OrderBy(b => b.CreatedAt)
                        .ThenBy(b => b.Id)
                        .ToList();

                    System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Found {batches.Count} batches with available stock");

                    foreach (var batch in batches)
                    {
                        if (remainingQuantity <= 0)
                            break;

                        decimal quantityFromBatch = Math.Min(batch.Quantity, remainingQuantity);

                        System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Deducting {quantityFromBatch} from batch {batch.BatchNumber} (Before: {batch.Quantity}, After: {batch.Quantity - quantityFromBatch})");

                        // Reduce quantity from this batch (entity is already tracked; no need to call Update)
                        batch.Quantity -= quantityFromBatch;

                        remainingQuantity -= quantityFromBatch;
                    }

                    // If there's still quantity needed but no batches available, log a warning
                    if (remainingQuantity > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] WARNING: Could not deduct {remainingQuantity} from batches - insufficient batch stock");
                        // For safety, we'll still deduct from the general stock to prevent negative stock issues
                        product.StockQuantity -= remainingQuantity;
                        dbContext.Products.Update(product);
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Product {product.Name} does not track batches - direct stock deduction");
                    // For non-batch products, just update the stock quantity directly
                    product.StockQuantity -= quantity;
                    dbContext.Products.Update(product);
                }

                // Ensure denormalized product stock matches sum of batches for batch-tracked products
                if (product.TrackBatches)
                {
                    try
                    {
                        var newTotal = product.Batches?.Sum(b => b.Quantity) ?? 0m;
                        product.StockQuantity = newTotal;
                        dbContext.Products.Update(product);
                        System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Synced denormalized stock to batch sum. New total: {newTotal}");
                    }
                    catch (Exception syncEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Error syncing product stock from batches: {syncEx.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Completed stock deduction for product {productId}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALE_STOCK_DEDUCT] Error deducting stock for product {productId}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Refreshes a specific product's data in the collections after stock update
        /// </summary>

        // Public safe wrapper to refresh a specific product by ID from views
        public async Task RefreshSpecificProductByIdSafe(int productId)
        {
            try
            {
                await RefreshSpecificProduct(productId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[SALES_VM] Error in RefreshSpecificProductByIdSafe for product {productId}: {ex.Message}");
            }
        }

        private async Task RefreshSpecificProduct(int productId)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Refreshing specific product {productId}...");

                // Get the updated product from database
                var updatedProduct = _dbService.GetProductById(productId);
                if (updatedProduct == null)
                {
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Product {productId} not found in database");
                    return;
                }

                // ✅ FIX: For batch-tracked products, ensure we have the latest batch data
                if (updatedProduct.TrackBatches)
                {
                    var batches = _dbService.GetBatchesForProduct(productId);
                    updatedProduct.Batches = batches;
                    System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Loaded {batches.Count} batches for product {productId}, total stock: {updatedProduct.GetTotalStockDecimal()}");
                }

                await Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    // Update the product in FilteredProducts collection
                    var filteredProduct = FilteredProducts.FirstOrDefault(p => p.Id == productId);
                    if (filteredProduct != null)
                    {
                        var index = FilteredProducts.IndexOf(filteredProduct);
                        FilteredProducts[index] = updatedProduct;
                        System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Updated product in FilteredProducts: {updatedProduct.Name}, Stock: {updatedProduct.StockQuantity}");
                    }
                    else
                    {
                        // ✅ FIX: If product not in FilteredProducts, add it (important for out-of-stock products that now have stock)
                        FilteredProducts.Add(updatedProduct);
                        System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Added product to FilteredProducts: {updatedProduct.Name}, Stock: {updatedProduct.StockQuantity}");
                    }

                    // Update the product in AllProducts collection
                    var allProduct = AllProducts.FirstOrDefault(p => p.Id == productId);
                    if (allProduct != null)
                    {
                        var index = AllProducts.IndexOf(allProduct);
                        AllProducts[index] = updatedProduct;
                        System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Updated product in AllProducts: {updatedProduct.Name}, Stock: {updatedProduct.StockQuantity}");


                    }
                    else
                    {
                        // ✅ FIX: If product not in AllProducts, add it
                        AllProducts.Add(updatedProduct);
                        System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Added product to AllProducts: {updatedProduct.Name}, Stock: {updatedProduct.StockQuantity}");
                    }

                    // Force property change notifications
                    OnPropertyChanged(nameof(FilteredProducts));
                    OnPropertyChanged(nameof(AllProducts));
                });

                // ✅ FIX: Fire ProductStockChanged event to notify UI components
                // Use GetTotalStockDecimal() for batch-tracked products to get accurate total
                var finalStockQuantity = updatedProduct.TrackBatches ? updatedProduct.GetTotalStockDecimal() : updatedProduct.StockQuantity;
                NotifyProductStockChanged(productId, finalStockQuantity, this);
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Fired ProductStockChanged event for product {productId}, new stock: {finalStockQuantity}");

                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Successfully refreshed product {productId} with new stock: {updatedProduct.StockQuantity}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[STOCK_RESERVATION] Error refreshing specific product {productId}: {ex.Message}");
                // Fall back to full refresh if specific refresh fails
                await RefreshProductData();
            }
        }

        private void IncreaseSelectedQuantity()
        {
            if (SelectedCartItem == null) return;

            var cartItem = SelectedCartItem;
            var product = cartItem.Product;
            if (product == null) return;

            // Determine increment based on weight-based flag
            decimal increment = product.IsWeightBased ? 0.1m : 1m;

            if (product.TrackBatches)
            {
                // Use batch-aware increment that splits lines and updates pricing
                IncreaseCartItemQuantityRespectingBatches(cartItem, increment);
            }
            else
            {
                // Stock limit check for non-batch products
                if (product.Id >= 0 && product.Type != ProductType.Service && cartItem.Quantity >= product.StockQuantity)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        string unit = product.IsWeightBased ? (product.UnitOfMeasure?.Abbreviation ?? "units") : "items";
                        MessageBox.Show($"Cannot add more. Only {product.StockQuantity:F3} {unit} available in stock.",
                            "Stock Limit Reached", MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                    return;
                }

                cartItem.Quantity += increment;
                CalculateTotals();
            }
        }

        /// <summary>
        /// Increase quantity for a cart item while respecting FIFO batches and batch pricing.
        /// If the increment crosses batch boundaries with different prices, splits into separate lines.
        /// </summary>
        public void IncreaseCartItemQuantityRespectingBatches(CartItem cartItem, decimal increment)
        {
            System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Enter IncreaseCartItemQuantityRespectingBatches for product {cartItem?.Product?.Id} - increment {increment}");
            if (cartItem == null || increment <= 0) return;
            var product = cartItem.Product;
            if (product == null)
            {
                return;
            }

            // Stock limit check (reuse existing logic where possible)
            if (product.Id >= 0 && product.Type != ProductType.Service && cartItem.Quantity >= product.StockQuantity)
            {
                Application.Current?.Dispatcher?.Invoke(() =>
                {
                    string unit = product.IsWeightBased ? (product.UnitOfMeasure?.Abbreviation ?? "units") : "items";
                    MessageBox.Show($"Cannot add more. Only {product.StockQuantity:F3} {unit} available in stock.",
                        "Stock Limit Reached", MessageBoxButton.OK, MessageBoxImage.Warning);
                });
                return;
            }

            if (!product.TrackBatches)
            {
                // Simple path for non-batch products
                cartItem.Quantity += increment;
                CalculateTotals();
                return;
            }

            try
            {
                var batches = _dbService.GetBatchesForProduct(product.Id)
                    .Where(b => b.Quantity > 0)
                    .OrderBy(b => b.CreatedAt)
                    .ThenBy(b => b.Id)
                    .ToList();

                System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Loaded {batches.Count} batches for product {product.Id}");
                foreach (var b in batches)
                {
                    System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Batch {b.BatchNumber} (Id={b.Id}) Qty={b.Quantity}, Price={b.SellingPrice}, CreatedAt={b.CreatedAt:O}");
                }

                if (batches == null || batches.Count == 0)
                {
                    // Fallback: treat as non-batch if batches not available
                    cartItem.Quantity += increment;
                    CalculateTotals();
                    return;
                }

                decimal remaining = increment;
                decimal? firstPrice = null;
                BatchStock boundaryBatch = null;

                // Allocate the increment across available FIFO batches
                foreach (var batch in batches)
                {
                    if (remaining <= 0) break;
                    var take = Math.Min(remaining, batch.Quantity);
                    if (take <= 0) continue;

                    var price = batch.SellingPrice;
                    if (firstPrice == null) firstPrice = price;
                    if (firstPrice != null && Math.Abs(price - firstPrice.Value) > 0.0001m && boundaryBatch == null)
                    {
                        boundaryBatch = batch; // first time we move to a different batch price
                    }

                    System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Allocating {take} from batch {batch.BatchNumber} at {price}");

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        // Try to find a line for this product at this batch price
                        var line = CurrentCart.Items.FirstOrDefault(i => i.Product.Id == product.Id && Math.Abs(i.UnitPrice - price) < 0.0001m);
                        if (line != null)
                        {
                            var prevQty = line.Quantity;
                            line.Quantity += take;
                            System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Merged into existing line (price {price}). Qty {prevQty} -> {line.Quantity}");
                            SelectedCartItem = line;
                        }
                        else
                        {
                            var newLine = new CartItem
                            {
                                Product = product,
                                Quantity = take,
                                UnitPrice = price,
                                BatchUnitPrice = price
                            };
                            CurrentCart.Items.Add(newLine);
                            System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Created new line at price {price} with qty {take}");
                            SelectedCartItem = newLine;
                        }
                    });

                    remaining -= take;
                }

                if (remaining > 0)
                {
                    // Should not happen due to earlier stock checks, but guard anyway
                    Application.Current?.Dispatcher?.Invoke(() =>
                    {
                        string unit = product.IsWeightBased ? (product.UnitOfMeasure?.Abbreviation ?? "units") : "items";
                        MessageBox.Show($"Only {product.StockQuantity:F3} {unit} available in stock.",
                            "Stock Limit", MessageBoxButton.OK, MessageBoxImage.Warning);
                    });
                }

                // Notify if we crossed a batch boundary and potentially changed price/expiry
                if (boundaryBatch != null)
                {
                    System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Boundary crossed into batch {boundaryBatch.BatchNumber} price {boundaryBatch.SellingPrice}");
                    BatchBoundaryCrossed?.Invoke(this, new BatchBoundaryEventArgs(product, boundaryBatch.SellingPrice, boundaryBatch.BatchNumber, boundaryBatch.ExpiryDate));
                }

                CalculateTotals();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[BATCH_INCREMENT] Error allocating increment across batches: {ex.Message}");
                // Fallback to simple increment to avoid blocking sales
                cartItem.Quantity += increment;
                CalculateTotals();
            }
        }

        private void DecreaseSelectedQuantity()
        {
            if (SelectedCartItem != null)
            {
                if (SelectedCartItem.Quantity > 1)
                {
                    SelectedCartItem.Quantity--;
                    CalculateTotals();
                }
                else
                {
                    RemoveFromCart(SelectedCartItem.Product.Id);
                }
            }
        }

        private void ShowPopularProducts()
        {
            _showingFavorites = false;
            OnPropertyChanged(nameof(ShowingFavorites));
            ShowingPopularItems = true;
            FilteredProducts.Clear();
            AllProducts.Clear();
            _ = LoadPopularProducts();
        }

        private async Task ToggleFavorite(Product product)
        {
            if (CurrentUser == null) return;

            try
            {
                using var __perfToggleFav = new POSSystem.Helpers.PerfTimer("Products.ToggleFavorite");
                var currentUserId = CurrentUser?.Id;
                if (!currentUserId.HasValue) return;

                var context = ((DatabaseService)_dbService).Context;
                {
                    var favorite = await context.Set<UserFavorite>()
                        .FirstOrDefaultAsync(f => f.UserId == currentUserId.Value && f.ProductId == product.Id);

                    bool newFavoriteStatus = favorite == null;

                    if (newFavoriteStatus)
                    {
                        // Add to favorites
                        context.Add(new UserFavorite
                        {
                            UserId = currentUserId.Value,
                            ProductId = product.Id,
                            CreatedAt = DateTime.Now
                        });
                    }
                    else
                    {
                        // Remove from favorites
                        context.Remove(favorite);
                    }

                    await context.SaveChangesAsync();

                    // Load the complete product data including batches
                    var updatedProduct = await context.Products
                        .AsNoTracking()
                        .Include(p => p.Category)
                        .Include(p => p.Barcodes)
                        .Include(p => p.Batches)
                        .FirstOrDefaultAsync(p => p.Id == product.Id);

                    if (updatedProduct != null)
                    {
                        updatedProduct.IsFavorited = newFavoriteStatus;

                        // Update the product's data in all collections
                        var productInFiltered = FilteredProducts.FirstOrDefault(p => p.Id == product.Id);
                        if (productInFiltered != null)
                        {
                            var index = FilteredProducts.IndexOf(productInFiltered);
                            FilteredProducts[index] = updatedProduct;
                        }

                        var productInAll = AllProducts.FirstOrDefault(p => p.Id == product.Id);
                        if (productInAll != null)
                        {
                            var index = AllProducts.IndexOf(productInAll);
                            AllProducts[index] = updatedProduct;
                        }

                        // If showing favorites and unfavoriting, remove from the collections
                        if (ShowingFavorites && !newFavoriteStatus)
                        {
                            await Application.Current.Dispatcher.InvokeAsync(() =>
                            {
                                FilteredProducts.Remove(productInFiltered);
                                AllProducts.Remove(productInAll);
                            });
                        }
                        // If showing favorites and favoriting, reload the favorites
                        else if (ShowingFavorites && newFavoriteStatus)
                        {
                            await LoadFavoriteProducts();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error toggling favorite: {ex.Message}");
                // Revert the UI state if the operation failed
                product.IsFavorited = !product.IsFavorited;
            }
        }

        private void ToggleShowFavorites()
        {
            if (_showingFavorites && ShowingPopularItems)
            {
                // If we're already showing favorites, switch to popular items
                _showingFavorites = false;
                OnPropertyChanged(nameof(ShowingFavorites));
                ShowingPopularItems = true;
                FilteredProducts.Clear();
                AllProducts.Clear();
                _ = LoadPopularProducts();
            }
            else
            {
                // Switch to favorites
                _showingFavorites = true;
                OnPropertyChanged(nameof(ShowingFavorites));
                ShowingPopularItems = false;
                FilteredProducts.Clear();
                AllProducts.Clear();
                _ = LoadFavoriteProducts();
            }
        }

        private async Task LoadFavoriteProducts()
        {
            using var __perfLoadFav = new POSSystem.Helpers.PerfTimer("Products.LoadFavorites");
                Debug.WriteLine($"[CART DEBUG] LoadFavoriteProducts called. CurrentUser: {CurrentUser?.Username ?? "null"}");

            var currentUserId = CurrentUser?.Id;
            if (!currentUserId.HasValue)
            {
                Debug.WriteLine("[CART DEBUG] No current user, falling back to popular products");
                await LoadPopularProducts();
                return;
            }

            try
            {
                using var __perfFavBatch = new POSSystem.Helpers.PerfTimer("UI.UpdateFavoritesBatch");
                    IsLoading = true;

                // Use DatabaseService method which has proper error handling for schema issues
                var favorites = await _dbService.GetUserFavoritesAsync(currentUserId.Value);

                POSSystem.Helpers.PerformanceDebugHelper.WriteCartDebug($"[CART DEBUG] Loaded {favorites.Count} favorite products for user {currentUserId}");

                // ✅ STOCK CONSISTENCY FIX: Ensure stock quantities are calculated correctly for batch-tracked products
                foreach (var product in favorites)
                {
                    product.IsFavorited = true;

                    if (product.TrackBatches && product.Batches != null && product.Batches.Any())
                    {
                        // For batch-tracked products, set stock quantity to the sum of batch quantities
                        var batchTotal = product.Batches.Sum(b => b.Quantity);
                        product.StockQuantity = batchTotal;
                        System.Diagnostics.Debug.WriteLine($"[SALES-STOCK-FIX] Favorite product {product.Name}: Set stock from batches = {batchTotal}");
                    }
                }

                // ✅ PERFORMANCE FIX: Use UIThreadProtection for better performance and safety
                await UIThreadProtection.BatchUpdateUIAsync(() =>
                {
                    CollectionDiffer.ApplyByKey(FilteredProducts, favorites, p => p.Id);
                    CollectionDiffer.ApplyByKey(AllProducts, favorites, p => p.Id);

                    _currentPage = 0;
                    _hasMoreItems = false;
                    _isSearching = false;
                    ShowingPopularItems = false;
                    _currentSearchText = null;
                    _lastSearchText = null;

                    Debug.WriteLine($"[CART DEBUG] Batch updated UI with {FilteredProducts.Count} favorite products");
                }, "LoadFavoriteProducts UI Update");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading favorites: {ex.Message}");

                // If it's a database schema issue, try to fall back to popular products
                if (ex.Message.Contains("no such column") || ex.Message.Contains("SQLite Error"))
                {
                    Debug.WriteLine("[CART DEBUG] Database schema issue detected, falling back to popular products");
                    await LoadPopularProducts();
                }
                else
                {
                    MessageBox.Show($"Error loading favorites: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task UpdateProductFavoriteStatus(IEnumerable<Product> products)
        {
            if (CurrentUser == null || products == null) return;

            var favoriteIds = await _dbService.GetUserFavoritesAsync(CurrentUser?.Id ?? 1);
            var favoriteIdSet = new HashSet<int>(favoriteIds.Select(p => p.Id));

            foreach (var product in products)
            {
                product.IsFavorited = favoriteIdSet.Contains(product.Id);
            }
        }

        // Sales Navigation Methods
        public async Task<bool> LoadSaleByInvoice(string invoiceNumber)
        {
            if (string.IsNullOrEmpty(invoiceNumber))
            {
                return false;
            }

            try
            {
                var context = ((DatabaseService)_dbService).Context;
                {
                    var sale = await context.Sales
                        .Include(s => s.Items)
                            .ThenInclude(si => si.Product)
                                .ThenInclude(p => p.Category)
                        .Include(s => s.Items)
                            .ThenInclude(si => si.Product)
                                .ThenInclude(p => p.Barcodes)
                        .Include(s => s.Customer)
                        .FirstOrDefaultAsync(s => s.InvoiceNumber == invoiceNumber);

                    if (sale == null)
                    {
                        MessageBox.Show($"No sale found with invoice number {invoiceNumber}",
                            "Not Found", MessageBoxButton.OK, MessageBoxImage.Information);
                        return false;
                    }

                    _currentSaleId = sale.Id;
                    CurrentLoadedSale = sale;
                    CurrentInvoiceNumber = sale.InvoiceNumber;
                    await LoadSaleIntoCart(sale);

                    IsEditMode = true;
                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading sale: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public async Task<bool> NavigateToNextSale()
        {
            try
            {
                var context = ((DatabaseService)_dbService).Context;
                {
                    Sale nextSale;

                    if (!_currentSaleId.HasValue)
                    {
                        // If no current sale is loaded, get the oldest sale
                        nextSale = await context.Sales
                            .Include(s => s.Items)
                                .ThenInclude(si => si.Product)
                                    .ThenInclude(p => p.Category)
                            .Include(s => s.Items)
                                .ThenInclude(si => si.Product)
                                    .ThenInclude(p => p.Barcodes)
                            .Include(s => s.Customer)
                            .OrderBy(s => s.Id)
                            .FirstOrDefaultAsync();

                        if (nextSale == null)
                        {
                            MessageBox.Show("No sales found in the database.",
                                "No Sales", MessageBoxButton.OK, MessageBoxImage.Information);
                            return false;
                        }
                    }
                    else
                    {
                        // Get the sale after the current one
                        nextSale = await context.Sales
                            .Include(s => s.Items)
                                .ThenInclude(si => si.Product)
                                    .ThenInclude(p => p.Category)
                            .Include(s => s.Items)
                                .ThenInclude(si => si.Product)
                                    .ThenInclude(p => p.Barcodes)
                            .Include(s => s.Customer)
                            .Where(s => s.Id > _currentSaleId.Value)
                            .OrderBy(s => s.Id)
                            .FirstOrDefaultAsync();

                        if (nextSale == null)
                        {
                            MessageBox.Show("You've reached the most recent sale.",
                                "End of Sales", MessageBoxButton.OK, MessageBoxImage.Information);
                            return false;
                        }
                    }

                    _currentSaleId = nextSale.Id;
                    CurrentLoadedSale = nextSale;
                    CurrentInvoiceNumber = nextSale.InvoiceNumber;
                    await LoadSaleIntoCart(nextSale);

                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error navigating to next sale: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        public async Task<bool> NavigateToPreviousSale()
        {
            try
            {
                var context = ((DatabaseService)_dbService).Context;
                {
                    Sale previousSale;

                    if (!_currentSaleId.HasValue)
                    {
                        // If no current sale is loaded, get the most recent sale
                        previousSale = await context.Sales
                            .Include(s => s.Items)
                                .ThenInclude(si => si.Product)
                                    .ThenInclude(p => p.Category)
                            .Include(s => s.Items)
                                .ThenInclude(si => si.Product)
                                    .ThenInclude(p => p.Barcodes)
                            .Include(s => s.Customer)
                            .OrderByDescending(s => s.Id)
                            .FirstOrDefaultAsync();

                        if (previousSale == null)
                        {
                            MessageBox.Show("No sales found in the database.",
                                "No Sales", MessageBoxButton.OK, MessageBoxImage.Information);
                            return false;
                        }
                    }
                    else
                    {
                        // Get the sale before the current one
                        previousSale = await context.Sales
                            .Include(s => s.Items)
                                .ThenInclude(si => si.Product)
                                    .ThenInclude(p => p.Category)
                            .Include(s => s.Items)
                                .ThenInclude(si => si.Product)
                                    .ThenInclude(p => p.Barcodes)
                            .Include(s => s.Customer)
                            .Where(s => s.Id < _currentSaleId.Value)
                            .OrderByDescending(s => s.Id)
                            .FirstOrDefaultAsync();

                        if (previousSale == null)
                        {
                            MessageBox.Show("You've reached the oldest sale.",
                                "Beginning of Sales", MessageBoxButton.OK, MessageBoxImage.Information);
                            return false;
                        }
                    }

                    _currentSaleId = previousSale.Id;
                    CurrentLoadedSale = previousSale;
                    CurrentInvoiceNumber = previousSale.InvoiceNumber;
                    await LoadSaleIntoCart(previousSale);

                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error navigating to previous sale: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        private async Task LoadSaleIntoCart(Sale sale)
        {
            // Store the loaded sale reference
            CurrentLoadedSale = sale;

            // If already in edit mode, reuse the current cart to avoid creating multiple tabs
            if (!IsEditMode)
            {
                // Only create a new cart if we're not already in edit mode
                CreateNewCart();
            }

            Cart saleCart = CurrentCart;
            saleCart.Name = $"Invoice #{sale.InvoiceNumber}";

            // Set customer if available
            if (sale.Customer != null)
            {
                SelectedCustomer = sale.Customer;
            }
            else
            {
                SelectedCustomer = null;
            }

            // Clear and add items from the sale
            saleCart.Items.Clear();
            foreach (var saleItem in sale.Items)
            {
                saleCart.Items.Add(new CartItem
                {
                    Product = saleItem.Product,
                    Quantity = saleItem.Quantity,
                    UnitPrice = saleItem.UnitPrice
                });
            }

            // Clear existing discounts
            saleCart.Discounts.Clear();

            // Add discounts if available
            // Note: Instead of checking Sale.Discounts, we will query discounts separately
            var context = ((DatabaseService)_dbService).Context;
            {
                var discounts = await context.Set<Discount>()
                    .Include(d => d.DiscountType)
                    .Where(d => d.SaleId == sale.Id && d.SaleItemId == null)
                    .ToListAsync();

                if (discounts.Any())
                {
                    foreach (var discount in discounts)
                    {
                        saleCart.Discounts.Add(discount);
                    }
                }
            }

            // Calculate totals
            saleCart.UpdateTotals();
            CalculateTotals();

            // Set edit mode
            IsEditMode = true;

            // Notify UI changes
            OnPropertyChanged(nameof(CurrentCart));
            OnPropertyChanged(nameof(HasCartItems));
        }

        public void ExitEditMode()
        {
            // Exit edit mode
            IsEditMode = false;
            CurrentLoadedSale = null;
            _currentSaleId = null;
            CurrentInvoiceNumber = null;

            // Create fresh cart
            CreateNewCart();
            OnPropertyChanged(nameof(CurrentCart));
        }

        public void ExitEditModeWithoutNewCart()
        {
            // Exit edit mode without creating a new cart
            IsEditMode = false;
            CurrentLoadedSale = null;
            _currentSaleId = null;
            CurrentInvoiceNumber = null;

            // Clear the current cart but don't create a new one
            if (CurrentCart != null)
            {
                CurrentCart.Items.Clear();
                CurrentCart.Discounts.Clear();
                CurrentCart.Name = $"Cart {Carts.IndexOf(CurrentCart) + 1}"; // Reset to default name
                CalculateTotals(); // Recalculate totals for the cleared cart
            }

            // Clear selected customer
            SelectedCustomer = null;

            OnPropertyChanged(nameof(CurrentCart));
            OnPropertyChanged(nameof(HasCartItems));
        }

        public async Task<bool> SaveModifiedSale()
        {
            if (!IsEditMode || CurrentLoadedSale == null || CurrentCart == null)
                return false;

            try
            {
                // Get the original sale ID
                int saleId = CurrentLoadedSale.Id;

                var context = ((DatabaseService)_dbService).Context;
                {
                    // Load the original sale
                    var sale = await context.Sales
                        .Include(s => s.Items)
                        .FirstOrDefaultAsync(s => s.Id == saleId);

                    if (sale == null)
                    {
                        MessageBox.Show("Original sale no longer exists", "Error",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }

                    // Update sale properties
                    sale.CustomerId = SelectedCustomer?.Id;
                    sale.Subtotal = CurrentCart.Subtotal;
                    sale.DiscountAmount = CurrentCart.DiscountAmount;
                    sale.TaxAmount = CurrentCart.TaxAmount;
                    sale.GrandTotal = CurrentCart.GrandTotal;
                    sale.Status = "Modified";

                    // Clear old items and add new ones
                    context.SaleItems.RemoveRange(sale.Items);
                    sale.Items.Clear();

                    foreach (var item in CurrentCart.Items)
                    {
                        sale.Items.Add(new SaleItem
                        {
                            ProductId = item.Product.Id,
                            Quantity = item.Quantity,
                            UnitPrice = item.UnitPrice,
                            Total = item.Total,
                            SaleId = saleId
                        });
                    }

                    // Remove existing discounts for this sale
                    var existingDiscounts = await context.Set<Discount>()
                        .Where(d => d.SaleId == saleId)
                        .ToListAsync();

                    if (existingDiscounts.Any())
                    {
                        context.RemoveRange(existingDiscounts);
                    }

                    // Add new discounts
                    foreach (var discount in CurrentCart.Discounts)
                    {
                        var newDiscount = new Discount
                        {
                            SaleId = saleId,
                            DiscountTypeId = discount.DiscountTypeId,
                            DiscountValue = discount.DiscountValue,
                            ReasonId = discount.ReasonId,
                            AppliedByUserId = CurrentUser?.Id ?? 1,
                            Comment = $"Modified: {discount.Comment}",
                            AppliedAt = DateTime.Now,
                            IsActive = true
                        };

                        context.Add(newDiscount);
                    }

                    await context.SaveChangesAsync();

                    MessageBox.Show($"Sale {sale.InvoiceNumber} successfully updated",
                        "Sale Updated", MessageBoxButton.OK, MessageBoxImage.Information);

                    // Exit edit mode after successful save without creating new cart
                    ExitEditModeWithoutNewCart();
                    return true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving modified sale: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        // Keyboard shortcut methods

        // Event to notify view to focus search box
        public event EventHandler FocusSearchBoxRequested;

        public void FocusSearchBox()
        {
            Debug.WriteLine("[KEYBOARD] SearchCommand executed - requesting search box focus");

            // Raise event to notify view to focus the search box
            FocusSearchBoxRequested?.Invoke(this, EventArgs.Empty);
        }

        public void ClearSearch()
        {
            // Clear search text and reset product list
            SearchText = string.Empty;
            _lastSearchText = string.Empty;
            ResetProductList();
            _ = RefreshProducts();
        }



        // Add a method to cancel ongoing searches
        public void CancelOngoingSearch()
        {
            if (_searchCancellation != null)
            {
                try
                {
                    _searchCancellation.Cancel();
                    _searchCancellation.Dispose();
                    _searchCancellation = new CancellationTokenSource();
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error canceling search: {ex.Message}");
                }
            }
        }
        private async Task<Product> OpenAddProductDialogWithExternalData(Product externalProduct, string barcode)
        {
            try
            {
                // Create a ProductsViewModel instance (we need this for the ProductDialog)
                var repositoryAdapter = (RepositoryServiceAdapter)App.ServiceProvider?.GetService(typeof(RepositoryServiceAdapter)) ??
                    throw new InvalidOperationException("RepositoryServiceAdapter not available from DI");
                var databaseService = _dbService as DatabaseService ??
                    throw new InvalidOperationException("DatabaseService not available");
                var productsViewModel = new ProductsViewModel(repositoryAdapter, new SimpleAlertService(), databaseService);
                await productsViewModel.LoadInitialData();

                // Create the ProductDialog with pre-filled external data
                var productDialog = new Views.Dialogs.ProductDialog(productsViewModel, "SalesDialog");

                // Pre-fill the dialog with external product data
                productDialog.AutoFillExternalProductInformation(externalProduct);

                // Set the barcode (this should be locked to prevent conflicts)
                productDialog.SetBarcodeFromExternal(barcode);

                // Show the dialog
                var result = await ShowProductDialog(productDialog);

                if (result is Product savedProduct)
                {
                    // Product was successfully saved
                    MessageBox.Show($"Product '{savedProduct.Name}' added successfully!", "Product Added",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    return savedProduct;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening Add Product dialog: {ex.Message}", "Error",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }

            return null;
        }

        private async Task<object> ShowProductDialog(Views.Dialogs.ProductDialog productDialog)
        {
            // Try different DialogHost identifiers in order of preference
            string[] dialogIdentifiers = { "SalesDialog", "MainSalesDialog", "MainDialog" };

            foreach (string identifier in dialogIdentifiers)
            {
                try
                {
                    return await DialogHost.Show(productDialog, identifier);
                }
                catch (InvalidOperationException)
                {
                    // This identifier doesn't exist, try the next one
                    continue;
                }
            }

            // If all dialog hosts fail, show a fallback message
            MessageBox.Show("Unable to open Add Product dialog. Please try again.", "Dialog Error",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return null;
        }


    }

    public static class PredicateBuilder
    {
        public static Expression<Func<T, bool>> False<T>() { return f => false; }

        public static Expression<Func<T, bool>> True<T>() { return f => true; }

        public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> expr1,
            Expression<Func<T, bool>> expr2)
        {
            var invokedExpr = System.Linq.Expressions.Expression.Invoke(expr2, expr1.Parameters);
            return System.Linq.Expressions.Expression.Lambda<Func<T, bool>>
                (System.Linq.Expressions.Expression.OrElse(expr1.Body, invokedExpr), expr1.Parameters);
        }
    }
}