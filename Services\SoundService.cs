using System;
using System.Media;
using System.Windows;
using System.Windows.Media;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using POSSystem.Services.Interfaces;

namespace POSSystem.Services
{
    public class SoundService : ISoundService
    {
        private readonly SoundPlayer _addToCartSound; // kept for backward compatibility
        private readonly byte[] _addToCartBytes;       // in-memory cache for reliable rapid playback
        private readonly MediaPlayer _mediaPlayer = new MediaPlayer(); // low-latency player with Stop support
        private const string SOUND_FILE_PATH = "Resources/Sounds/cart-add.wav";
        private double _volume = 1.0;
        private bool _isMuted = false;

        // Audio interruption mechanism for rapid playback
        private CancellationTokenSource _currentPlaybackCancellation;
        private readonly object _playbackLock = new object();

        public SoundService()
        {
            try
            {
                string soundPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, SOUND_FILE_PATH);
                if (File.Exists(soundPath))
                {
                    // Prepare both players
                    _addToCartSound = new SoundPlayer(soundPath);
                    _addToCartSound.LoadAsync(); // Preload

                    _addToCartBytes = File.ReadAllBytes(soundPath);

                    // Configure MediaPlayer for ultra-low latency with Stop support
                    _mediaPlayer.Open(new Uri(soundPath));
                    _mediaPlayer.Volume = _volume; // 0..1 scale
                    _mediaPlayer.MediaFailed += (s, e) =>
                    {
                        System.Diagnostics.Debug.WriteLine($"[SOUND] MediaPlayer failed: {e.ErrorException?.Message}");
                    };
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"Sound file not found at: {soundPath}");
                    _addToCartSound = null;
                    _addToCartBytes = null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing sound service: {ex.Message}");
                _addToCartSound = null;
                _addToCartBytes = null;
            }
        }

        public void PlayAddToCartSound()
        {
            if (_isMuted) return;

            lock (_playbackLock)
            {
                try
                {
                    if (_mediaPlayer != null)
                    {
                        // Ultra-low latency: Stop, seek to 0, and Play
                        _mediaPlayer.Stop();
                        _mediaPlayer.Position = TimeSpan.Zero;
                        _mediaPlayer.Volume = _isMuted ? 0 : _volume;
                        _mediaPlayer.Play();
                        System.Diagnostics.Debug.WriteLine("[SOUND] MediaPlayer cart add sound played (Stop->Seek0->Play)");
                    }
                    else if (_addToCartSound != null)
                    {
                        // Legacy fallback: Stop+Play
                        _addToCartSound.Stop();
                        _addToCartSound.Play();
                        System.Diagnostics.Debug.WriteLine("[SOUND] SoundPlayer cart add sound played (Stop+Play)");
                    }
                    else if (_addToCartBytes != null)
                    {
                        // Fallback: play from memory without queuing
                        _ = Task.Run(() =>
                        {
                            try
                            {
                                using (var ms = new MemoryStream(_addToCartBytes, writable: false))
                                using (var player = new SoundPlayer(ms))
                                {
                                    player.Load();
                                    player.Play();
                                }
                            }
                            catch (Exception innerEx)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SOUND] Memory fallback error: {innerEx.Message}");
                                SystemSounds.Asterisk.Play();
                            }
                        });
                    }
                    else
                    {
                        // Fallback to system sound
                        System.Diagnostics.Debug.WriteLine("[SOUND] Using system sound for cart add (no custom sound available)");
                        SystemSounds.Asterisk.Play();
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but don't show it to the user since sound is not critical
                    System.Diagnostics.Debug.WriteLine($"[SOUND] Error playing cart add sound: {ex.Message}");
                    // Final fallback
                    try { SystemSounds.Beep.Play(); } catch { }
                }
            }
        }

        public void PlaySound(string soundName)
        {
            if (_isMuted) return;

            try
            {
                switch (soundName.ToLower())
                {
                    case "cart-add":
                        PlayAddToCartSound();
                        break;
                    case "error":
                        PlayErrorSound();
                        break;
                    case "success":
                        PlaySuccessSound();
                        break;
                    default:
                        SystemSounds.Beep.Play();
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error playing sound '{soundName}': {ex.Message}");
            }
        }

        public void PlayCartAddSound()
        {
            PlayAddToCartSound();
        }

        public void PlayErrorSound()
        {
            if (!_isMuted)
                SystemSounds.Hand.Play();
        }

        public void PlaySuccessSound()
        {
            if (!_isMuted)
            {
                // Use a more pleasant success sound for payment completion
                try
                {
                    // Try to play a custom success sound if available
                    string successSoundPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Resources/Sounds/payment-success.wav");
                    System.Diagnostics.Debug.WriteLine($"[SOUND] Looking for custom success sound at: {successSoundPath}");

                    if (File.Exists(successSoundPath))
                    {
                        System.Diagnostics.Debug.WriteLine("[SOUND] Custom success sound file found - playing custom sound");
                        _ = Task.Run(() =>
                        {
                            try
                            {
                                using (var player = new SoundPlayer(successSoundPath))
                                {
                                    player.LoadAsync();
                                    player.PlaySync();
                                }
                                System.Diagnostics.Debug.WriteLine("[SOUND] Custom success sound played successfully");
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"[SOUND] Error playing custom success sound: {ex.Message}");
                                // Fallback to system sound
                                SystemSounds.Asterisk.Play();
                                System.Diagnostics.Debug.WriteLine("[SOUND] Fallback system sound played");
                            }
                        });
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("[SOUND] Custom success sound file not found - using fallback system sound");
                        // Fallback to system sound
                        SystemSounds.Asterisk.Play();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in PlaySuccessSound: {ex.Message}");
                    // Final fallback
                    SystemSounds.Asterisk.Play();
                }
            }
        }

        public void SetVolume(double volume)
        {
            _volume = Math.Max(0.0, Math.Min(1.0, volume));
            try { _mediaPlayer.Volume = _volume; } catch { }
        }

        public bool IsMuted
        {
            get => _isMuted;
            set
            {
                _isMuted = value;
                try { _mediaPlayer.IsMuted = _isMuted; } catch { }
            }
        }

        public void Dispose()
        {
            _currentPlaybackCancellation?.Cancel();
            _currentPlaybackCancellation?.Dispose();
            _addToCartSound?.Dispose();
            try { _mediaPlayer.Close(); } catch { }
        }
    }
}
